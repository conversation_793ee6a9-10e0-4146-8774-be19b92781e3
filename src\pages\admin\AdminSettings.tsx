import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Settings,
  Globe,
  Shield,
  Mail,
  Database,
  Palette,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  MessageCircle,
  Phone,
  Clock,
  ExternalLink
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { toast } from 'sonner';
import { useChatSettings, useCloudPanoSettings } from '@/hooks/useChatSettings';

const AdminSettings = () => {
  const [isSaving, setIsSaving] = useState(false);

  // Chat settings hook
  const { chatSettings, isLoading: chatLoading, updateChatSettings, isUpdating } = useChatSettings();

  // CloudPano settings hook
  const { cloudPanoSettings, isLoading: cloudPanoLoading, updateCloudPanoSettings, isUpdating: cloudPanoUpdating } = useCloudPanoSettings();
  
  // General Settings
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'VirtualRealTour',
    siteDescription: 'Professional 360° Virtual Tours in Nigeria',
    siteUrl: 'https://virtualrealtour.ng',
    adminEmail: '<EMAIL>',
    timezone: 'Africa/Lagos',
    language: 'en',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true
  });

  // Email Settings
  const [emailSettings, setEmailSettings] = useState({
    smtpHost: 'smtp.gmail.com',
    smtpPort: '587',
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'VirtualRealTour',
    emailEnabled: true
  });

  // Security Settings
  const [securitySettings, setSecuritySettings] = useState({
    passwordMinLength: 8,
    requireSpecialChars: true,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    twoFactorEnabled: false,
    ipWhitelist: '',
    corsOrigins: 'https://virtualrealtour.ng'
  });

  // Storage Settings
  const [storageSettings, setStorageSettings] = useState({
    maxFileSize: 50,
    allowedFileTypes: 'jpg,jpeg,png,gif,mp4,mov',
    storageProvider: 'supabase',
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: 30
  });

  // Theme Settings
  const [themeSettings, setThemeSettings] = useState({
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    accentColor: '#10b981',
    darkMode: true,
    customCSS: ''
  });

  const handleSaveSettings = async (settingsType: string) => {
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSaving(false);
    toast.success(`${settingsType} settings saved successfully`);
  };

  const handleTestEmail = async () => {
    toast.info('Sending test email...');
    
    // Simulate email test
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast.success('Test email sent successfully');
  };

  const handleBackupNow = async () => {
    toast.info('Starting backup...');
    
    // Simulate backup process
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    toast.success('Backup completed successfully');
  };

  return (
    <AdminLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">System Settings</h1>
            <p className="text-muted-foreground">Configure platform settings and preferences</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleBackupNow}>
              <Database className="w-4 h-4 mr-2" />
              Backup Now
            </Button>
          </div>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="cloudpano">CloudPano</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  General Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={generalSettings.siteName}
                      onChange={(e) => setGeneralSettings({...generalSettings, siteName: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteUrl">Site URL</Label>
                    <Input
                      id="siteUrl"
                      value={generalSettings.siteUrl}
                      onChange={(e) => setGeneralSettings({...generalSettings, siteUrl: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="adminEmail">Admin Email</Label>
                    <Input
                      id="adminEmail"
                      type="email"
                      value={generalSettings.adminEmail}
                      onChange={(e) => setGeneralSettings({...generalSettings, adminEmail: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select value={generalSettings.timezone} onValueChange={(value) => setGeneralSettings({...generalSettings, timezone: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Africa/Lagos">Africa/Lagos (WAT)</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">America/New_York (EST)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={generalSettings.siteDescription}
                    onChange={(e) => setGeneralSettings({...generalSettings, siteDescription: e.target.value})}
                    rows={3}
                  />
                </div>

                <div className="space-y-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Maintenance Mode</Label>
                      <p className="text-sm text-muted-foreground">Temporarily disable public access</p>
                    </div>
                    <Switch
                      checked={generalSettings.maintenanceMode}
                      onCheckedChange={(checked) => setGeneralSettings({...generalSettings, maintenanceMode: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>User Registration</Label>
                      <p className="text-sm text-muted-foreground">Allow new user registrations</p>
                    </div>
                    <Switch
                      checked={generalSettings.registrationEnabled}
                      onCheckedChange={(checked) => setGeneralSettings({...generalSettings, registrationEnabled: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Email Verification</Label>
                      <p className="text-sm text-muted-foreground">Require email verification for new accounts</p>
                    </div>
                    <Switch
                      checked={generalSettings.emailVerificationRequired}
                      onCheckedChange={(checked) => setGeneralSettings({...generalSettings, emailVerificationRequired: checked})}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings('General')} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save General Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="email" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Email Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span>Email service is configured and working</span>
                  </div>
                  <Button variant="outline" onClick={handleTestEmail}>
                    Send Test Email
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtpHost">SMTP Host</Label>
                    <Input
                      id="smtpHost"
                      value={emailSettings.smtpHost}
                      onChange={(e) => setEmailSettings({...emailSettings, smtpHost: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtpPort">SMTP Port</Label>
                    <Input
                      id="smtpPort"
                      value={emailSettings.smtpPort}
                      onChange={(e) => setEmailSettings({...emailSettings, smtpPort: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fromEmail">From Email</Label>
                    <Input
                      id="fromEmail"
                      type="email"
                      value={emailSettings.fromEmail}
                      onChange={(e) => setEmailSettings({...emailSettings, fromEmail: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fromName">From Name</Label>
                    <Input
                      id="fromName"
                      value={emailSettings.fromName}
                      onChange={(e) => setEmailSettings({...emailSettings, fromName: e.target.value})}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings('Email')} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Email Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                    <Input
                      id="passwordMinLength"
                      type="number"
                      value={securitySettings.passwordMinLength}
                      onChange={(e) => setSecuritySettings({...securitySettings, passwordMinLength: parseInt(e.target.value)})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                    <Input
                      id="maxLoginAttempts"
                      type="number"
                      value={securitySettings.maxLoginAttempts}
                      onChange={(e) => setSecuritySettings({...securitySettings, maxLoginAttempts: parseInt(e.target.value)})}
                    />
                  </div>
                </div>

                <div className="space-y-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Require Special Characters</Label>
                      <p className="text-sm text-muted-foreground">Passwords must contain special characters</p>
                    </div>
                    <Switch
                      checked={securitySettings.requireSpecialChars}
                      onCheckedChange={(checked) => setSecuritySettings({...securitySettings, requireSpecialChars: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Two-Factor Authentication</Label>
                      <p className="text-sm text-muted-foreground">Enable 2FA for admin accounts</p>
                    </div>
                    <Switch
                      checked={securitySettings.twoFactorEnabled}
                      onCheckedChange={(checked) => setSecuritySettings({...securitySettings, twoFactorEnabled: checked})}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings('Security')} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Security Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="storage" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Storage & Backup
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxFileSize">Max File Size (MB)</Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={storageSettings.maxFileSize}
                      onChange={(e) => setStorageSettings({...storageSettings, maxFileSize: parseInt(e.target.value)})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retentionDays">Backup Retention (days)</Label>
                    <Input
                      id="retentionDays"
                      type="number"
                      value={storageSettings.retentionDays}
                      onChange={(e) => setStorageSettings({...storageSettings, retentionDays: parseInt(e.target.value)})}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="allowedFileTypes">Allowed File Types</Label>
                  <Input
                    id="allowedFileTypes"
                    value={storageSettings.allowedFileTypes}
                    onChange={(e) => setStorageSettings({...storageSettings, allowedFileTypes: e.target.value})}
                    placeholder="jpg,jpeg,png,gif,mp4,mov"
                  />
                </div>

                <div className="space-y-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Automatic Backup</Label>
                      <p className="text-sm text-muted-foreground">Enable scheduled backups</p>
                    </div>
                    <Switch
                      checked={storageSettings.autoBackup}
                      onCheckedChange={(checked) => setStorageSettings({...storageSettings, autoBackup: checked})}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings('Storage')} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Storage Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Theme Customization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={themeSettings.primaryColor}
                        onChange={(e) => setThemeSettings({...themeSettings, primaryColor: e.target.value})}
                        className="w-16 h-10"
                      />
                      <Input
                        value={themeSettings.primaryColor}
                        onChange={(e) => setThemeSettings({...themeSettings, primaryColor: e.target.value})}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={themeSettings.secondaryColor}
                        onChange={(e) => setThemeSettings({...themeSettings, secondaryColor: e.target.value})}
                        className="w-16 h-10"
                      />
                      <Input
                        value={themeSettings.secondaryColor}
                        onChange={(e) => setThemeSettings({...themeSettings, secondaryColor: e.target.value})}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="accentColor"
                        type="color"
                        value={themeSettings.accentColor}
                        onChange={(e) => setThemeSettings({...themeSettings, accentColor: e.target.value})}
                        className="w-16 h-10"
                      />
                      <Input
                        value={themeSettings.accentColor}
                        onChange={(e) => setThemeSettings({...themeSettings, accentColor: e.target.value})}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customCSS">Custom CSS</Label>
                  <Textarea
                    id="customCSS"
                    value={themeSettings.customCSS}
                    onChange={(e) => setThemeSettings({...themeSettings, customCSS: e.target.value})}
                    rows={6}
                    placeholder="/* Add your custom CSS here */"
                    className="font-mono text-sm"
                  />
                </div>

                <Button onClick={() => handleSaveSettings('Theme')} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Theme Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="chat" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Chat Widget Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {chatLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin" />
                    <span className="ml-2">Loading chat settings...</span>
                  </div>
                ) : (
                  <>
                    {/* Chat Widget Toggle */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <Label className="text-base font-medium">Enable Chat Widget</Label>
                        <p className="text-sm text-muted-foreground">
                          Show the floating WhatsApp chat button on your website
                        </p>
                      </div>
                      <Switch
                        checked={chatSettings?.enabled || false}
                        onCheckedChange={(checked) => updateChatSettings({ enabled: checked })}
                        disabled={isUpdating}
                      />
                    </div>

                    {/* Business Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        Business Information
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="businessName">Business Name</Label>
                          <Input
                            id="businessName"
                            value={chatSettings?.businessName || ''}
                            onChange={(e) => updateChatSettings({ businessName: e.target.value })}
                            placeholder="Your Business Name"
                            disabled={isUpdating}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="businessPhone">WhatsApp Number</Label>
                          <Input
                            id="businessPhone"
                            value={chatSettings?.businessPhone || ''}
                            onChange={(e) => updateChatSettings({ businessPhone: e.target.value })}
                            placeholder="+234XXXXXXXXXX"
                            disabled={isUpdating}
                          />
                          <p className="text-xs text-muted-foreground">
                            Include country code (e.g., +234 for Nigeria)
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="defaultMessage">Default Message</Label>
                        <Textarea
                          id="defaultMessage"
                          value={chatSettings?.defaultMessage || ''}
                          onChange={(e) => updateChatSettings({ defaultMessage: e.target.value })}
                          placeholder="Hi! I need help with..."
                          rows={3}
                          disabled={isUpdating}
                        />
                        <p className="text-xs text-muted-foreground">
                          This message will be pre-filled when users click the chat button
                        </p>
                      </div>
                    </div>

                    {/* Widget Appearance */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Widget Appearance</h3>

                      <div className="space-y-2">
                        <Label htmlFor="chatPosition">Position</Label>
                        <Select
                          value={chatSettings?.position || 'bottom-right'}
                          onValueChange={(value: 'bottom-right' | 'bottom-left') =>
                            updateChatSettings({ position: value })
                          }
                          disabled={isUpdating}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select position" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bottom-right">Bottom Right</SelectItem>
                            <SelectItem value="bottom-left">Bottom Left</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Business Hours */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          Business Hours
                        </h3>
                        <Switch
                          checked={chatSettings?.businessHours?.enabled || false}
                          onCheckedChange={(checked) =>
                            updateChatSettings({
                              businessHours: {
                                ...chatSettings?.businessHours,
                                enabled: checked
                              }
                            })
                          }
                          disabled={isUpdating}
                        />
                      </div>

                      {chatSettings?.businessHours?.enabled && (
                        <div className="p-4 border rounded-lg bg-muted/50">
                          <p className="text-sm text-muted-foreground mb-4">
                            Configure when the chat widget should be available. Outside business hours,
                            users will see an offline message.
                          </p>
                          <div className="text-sm">
                            <p><strong>Timezone:</strong> {chatSettings?.businessHours?.timezone || 'Africa/Lagos'}</p>
                            <p className="mt-2 text-muted-foreground">
                              Business hours configuration will be available in a future update.
                            </p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end pt-4">
                      <Button
                        onClick={() => toast.success('Chat settings are automatically saved')}
                        disabled={isUpdating}
                        className="flex items-center gap-2"
                      >
                        {isUpdating ? (
                          <RefreshCw className="w-4 h-4 animate-spin" />
                        ) : (
                          <CheckCircle className="w-4 h-4" />
                        )}
                        {isUpdating ? 'Saving...' : 'Settings Auto-Saved'}
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cloudpano" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  CloudPano Integration Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {cloudPanoLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin" />
                    <span className="ml-2">Loading CloudPano settings...</span>
                  </div>
                ) : (
                  <>
                    {/* CloudPano Toggle */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <Label className="text-base font-medium">Enable CloudPano Integration</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow users to create tours using CloudPano platform
                        </p>
                      </div>
                      <Switch
                        checked={cloudPanoSettings?.enabled || false}
                        onCheckedChange={(checked) => updateCloudPanoSettings({ enabled: checked })}
                        disabled={cloudPanoUpdating}
                      />
                    </div>

                    {/* Account Configuration */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <Settings className="w-4 h-4" />
                        CloudPano Account Configuration
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="cloudpanoEmail">Account Email</Label>
                          <Input
                            id="cloudpanoEmail"
                            type="email"
                            value={cloudPanoSettings?.accountEmail || ''}
                            onChange={(e) => updateCloudPanoSettings({ accountEmail: e.target.value })}
                            placeholder="<EMAIL>"
                            disabled={cloudPanoUpdating}
                          />
                          <p className="text-xs text-muted-foreground">
                            Your CloudPano account email address
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="cloudpanoPassword">Account Password</Label>
                          <Input
                            id="cloudpanoPassword"
                            type="password"
                            value={cloudPanoSettings?.accountPassword || ''}
                            onChange={(e) => updateCloudPanoSettings({ accountPassword: e.target.value })}
                            placeholder="••••••••"
                            disabled={cloudPanoUpdating}
                          />
                          <p className="text-xs text-muted-foreground">
                            Your CloudPano account password (stored securely)
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cloudpanoApiKey">API Key (Optional)</Label>
                        <Input
                          id="cloudpanoApiKey"
                          value={cloudPanoSettings?.apiKey || ''}
                          onChange={(e) => updateCloudPanoSettings({ apiKey: e.target.value })}
                          placeholder="Enter CloudPano API key for advanced features"
                          disabled={cloudPanoUpdating}
                        />
                        <p className="text-xs text-muted-foreground">
                          API key for programmatic tour creation (optional for widget mode)
                        </p>
                      </div>
                    </div>

                    {/* URL Configuration */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">URL Configuration</h3>

                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="cloudpanoBaseUrl">CloudPano Base URL</Label>
                          <Input
                            id="cloudpanoBaseUrl"
                            value={cloudPanoSettings?.baseUrl || ''}
                            onChange={(e) => updateCloudPanoSettings({ baseUrl: e.target.value })}
                            placeholder="https://app.cloudpano.com"
                            disabled={cloudPanoUpdating}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="cloudpanoCreateUrl">Tour Creation URL</Label>
                          <Input
                            id="cloudpanoCreateUrl"
                            value={cloudPanoSettings?.createTourUrl || ''}
                            onChange={(e) => updateCloudPanoSettings({ createTourUrl: e.target.value })}
                            placeholder="https://app.cloudpano.com/"
                            disabled={cloudPanoUpdating}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="cloudpanoViewerUrl">Viewer Base URL</Label>
                          <Input
                            id="cloudpanoViewerUrl"
                            value={cloudPanoSettings?.viewerUrl || ''}
                            onChange={(e) => updateCloudPanoSettings({ viewerUrl: e.target.value })}
                            placeholder="https://viewer.cloudpano.com"
                            disabled={cloudPanoUpdating}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Test Connection */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Connection Test</h3>
                      <div className="p-4 border rounded-lg bg-muted/50">
                        <p className="text-sm text-muted-foreground mb-4">
                          Test your CloudPano configuration to ensure everything is working properly.
                        </p>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => window.open(cloudPanoSettings?.createTourUrl || 'https://app.cloudpano.com/', '_blank')}
                            variant="outline"
                            size="sm"
                          >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Test Create URL
                          </Button>
                          <Button
                            onClick={() => window.open(cloudPanoSettings?.viewerUrl || 'https://viewer.cloudpano.com', '_blank')}
                            variant="outline"
                            size="sm"
                          >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Test Viewer URL
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end pt-4">
                      <Button
                        onClick={() => toast.success('CloudPano settings are automatically saved')}
                        disabled={cloudPanoUpdating}
                        className="flex items-center gap-2"
                      >
                        {cloudPanoUpdating ? (
                          <RefreshCw className="w-4 h-4 animate-spin" />
                        ) : (
                          <CheckCircle className="w-4 h-4" />
                        )}
                        {cloudPanoUpdating ? 'Saving...' : 'Settings Auto-Saved'}
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
