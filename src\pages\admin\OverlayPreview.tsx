import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';

const OverlayPreview = () => {
  const [show, setShow] = useState(false);
  return (
    <AdminLayout>
      <Card className="max-w-2xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>Overlay / Glass UI Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setShow(s => !s)} className="mb-4">{show ? 'Hide Overlay' : 'Show Overlay'}</Button>
          {show && (
            <div className="glass-ui-demo p-8 rounded-xl shadow-lg border bg-white/30 backdrop-blur-md">
              <h2 className="text-xl font-bold mb-2">Glass UI Overlay Example</h2>
              <p>This is a live preview of the glassmorphism overlay style used in the app.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default OverlayPreview;
