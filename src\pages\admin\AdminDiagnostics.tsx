/**
 * Admin System Diagnostics Page
 * System diagnostics moved to independent testing framework
 */

import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TestTube, ExternalLink, Info } from 'lucide-react';

const AdminDiagnostics = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">System Diagnostics</h1>
          <p className="text-muted-foreground">
            System diagnostics and testing tools have been moved to the independent testing framework
          </p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Testing Framework:</strong> All diagnostic tools, system tests, and quality assurance
            utilities have been moved to the git-ignored testing framework to keep the production
            application clean and focused on business functionality.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="w-5 h-5" />
              Testing Framework
            </CardTitle>
            <CardDescription>
              Independent testing environment for comprehensive system validation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Unit Tests</h3>
                  <p className="text-sm text-muted-foreground">
                    Component and service testing with React Testing Library
                  </p>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Integration Tests</h3>
                  <p className="text-sm text-muted-foreground">
                    API integration and service layer testing
                  </p>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">E2E Tests</h3>
                  <p className="text-sm text-muted-foreground">
                    End-to-end user workflow validation
                  </p>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Performance Tests</h3>
                  <p className="text-sm text-muted-foreground">
                    Load testing and performance monitoring
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Testing Framework Location</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  The testing framework is located in the <code>testing-framework/</code> directory
                  and is git-ignored to maintain separation from production code.
                </p>
                <div className="flex items-center gap-2 text-sm">
                  <ExternalLink className="w-4 h-4" />
                  <span>See testing-framework/README.md for usage instructions</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminDiagnostics;
