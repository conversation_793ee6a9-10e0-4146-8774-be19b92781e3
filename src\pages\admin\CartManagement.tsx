import { useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ShoppingCart, { CartItem, CustomerInfo } from '@/components/commerce/ShoppingCart';

const initialCart: CartItem[] = [];

const CartManagement = () => {
  const [cart, setCart] = useState<CartItem[]>(initialCart);
  const [open, setOpen] = useState(true);

  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    setCart(prev => prev.map(item => item.id === itemId ? { ...item, quantity } : item));
  };
  const handleRemoveItem = (itemId: string) => {
    setCart(prev => prev.filter(item => item.id !== itemId));
  };
  const handleCheckout = (customerInfo: CustomerInfo) => {
    // Simulate checkout
    setCart([]);
  };

  return (
    <AdminLayout>
      <div className="max-w-2xl mx-auto mt-8">
        <h1 className="text-2xl font-bold mb-4">Cart Management</h1>
        <ShoppingCart
          items={cart}
          isOpen={open}
          onOpenChange={setOpen}
          onUpdateQuantity={handleUpdateQuantity}
          onRemoveItem={handleRemoveItem}
          onCheckout={handleCheckout}
        />
      </div>
    </AdminLayout>
  );
};

export default CartManagement;
