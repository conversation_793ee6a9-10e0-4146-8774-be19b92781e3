import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye, 
  MapPin, 
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { supabase } from '@/lib/supabase';

const AdminAnalytics = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch analytics data
  const { data: analytics, isLoading, refetch } = useQuery({
    queryKey: ['admin-analytics', timeRange],
    queryFn: async () => {
      // Get tours data
      const { data: tours } = await supabase
        .from('tours')
        .select('*');

      // Get users data
      const { data: users } = await supabase
        .from('profiles')
        .select('*');

      // Get analytics data
      const { data: tourAnalytics } = await supabase
        .from('tour_analytics')
        .select('*');

      // Calculate metrics
      const totalTours = tours?.length || 0;
      const totalUsers = users?.length || 0;
      const totalViews = tours?.reduce((sum, tour) => sum + (tour.views || 0), 0) || 0;
      const publishedTours = tours?.filter(tour => tour.status === 'published').length || 0;

      // Mock data for charts (in real app, this would be calculated from actual analytics)
      const viewsOverTime = [
        { date: '2024-01-01', views: 120 },
        { date: '2024-01-02', views: 150 },
        { date: '2024-01-03', views: 180 },
        { date: '2024-01-04', views: 200 },
        { date: '2024-01-05', views: 170 },
        { date: '2024-01-06', views: 220 },
        { date: '2024-01-07', views: 250 },
      ];

      const topTours = tours?.sort((a, b) => (b.views || 0) - (a.views || 0)).slice(0, 5) || [];

      const categoryStats = tours?.reduce((acc, tour) => {
        acc[tour.category] = (acc[tour.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      return {
        totalTours,
        totalUsers,
        totalViews,
        publishedTours,
        viewsOverTime,
        topTours,
        categoryStats,
        tourAnalytics: tourAnalytics || []
      };
    },
  });

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  };

  const exportData = () => {
    // In a real app, this would generate and download a CSV/Excel file
    console.log('Exporting analytics data...');
  };

  const stats = [
    {
      title: 'Total Tours',
      value: analytics?.totalTours || 0,
      change: '+12%',
      trend: 'up',
      icon: MapPin,
      color: 'text-blue-600'
    },
    {
      title: 'Total Views',
      value: analytics?.totalViews || 0,
      change: '+18%',
      trend: 'up',
      icon: Eye,
      color: 'text-green-600'
    },
    {
      title: 'Active Users',
      value: analytics?.totalUsers || 0,
      change: '+8%',
      trend: 'up',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      title: 'Published Tours',
      value: analytics?.publishedTours || 0,
      change: '+15%',
      trend: 'up',
      icon: BarChart3,
      color: 'text-orange-600'
    }
  ];

  return (
    <AdminLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
            <p className="text-muted-foreground">Platform performance and insights</p>
          </div>
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={exportData}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                    <p className="text-3xl font-bold">{stat.value.toLocaleString()}</p>
                    <p className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'} flex items-center gap-1`}>
                      <TrendingUp className="w-3 h-3" />
                      {stat.change} from last period
                    </p>
                  </div>
                  <div className={`p-3 rounded-full bg-muted ${stat.color}`}>
                    <stat.icon className="w-6 h-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tours">Tour Analytics</TabsTrigger>
            <TabsTrigger value="users">User Analytics</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Views Over Time Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Views Over Time
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Chart visualization would go here</p>
                      <p className="text-sm text-muted-foreground">Integration with Chart.js or Recharts</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Category Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Tours by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics?.categoryStats || {}).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="capitalize">{category}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 bg-muted rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full" 
                              style={{ width: `${(count / (analytics?.totalTours || 1)) * 100}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium">{count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Performing Tours */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Tours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics?.topTours.map((tour, index) => (
                    <div key={tour.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">#{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold">{tour.title}</h3>
                        <p className="text-sm text-muted-foreground">{tour.location}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{tour.views || 0} views</p>
                        <p className="text-sm text-muted-foreground capitalize">{tour.category}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tours" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Tour Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Published</span>
                      <span className="font-semibold">{analytics?.publishedTours || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Draft</span>
                      <span className="font-semibold">{(analytics?.totalTours || 0) - (analytics?.publishedTours || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total</span>
                      <span className="font-semibold">{analytics?.totalTours || 0}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Average Views</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <p className="text-3xl font-bold">
                      {analytics?.totalTours ? Math.round((analytics?.totalViews || 0) / analytics.totalTours) : 0}
                    </p>
                    <p className="text-muted-foreground">views per tour</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Engagement Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <p className="text-3xl font-bold">78%</p>
                    <p className="text-muted-foreground">average engagement</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Growth</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
                    <div className="text-center">
                      <Users className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">User growth chart</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Active Users</span>
                      <span className="font-semibold">{Math.round((analytics?.totalUsers || 0) * 0.7)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>New Users (7d)</span>
                      <span className="font-semibold">{Math.round((analytics?.totalUsers || 0) * 0.1)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Returning Users</span>
                      <span className="font-semibold">{Math.round((analytics?.totalUsers || 0) * 0.6)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Page Load Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <p className="text-3xl font-bold">1.2s</p>
                    <p className="text-muted-foreground">average load time</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Bounce Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <p className="text-3xl font-bold">24%</p>
                    <p className="text-muted-foreground">bounce rate</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Session Duration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <p className="text-3xl font-bold">4.2m</p>
                    <p className="text-muted-foreground">avg session</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminAnalytics;
