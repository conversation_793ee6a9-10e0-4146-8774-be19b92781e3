import { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';

const mockLogs = [
  { id: 1, type: 'error', message: 'Failed to fetch WooCommerce product', timestamp: '2025-07-01 10:00' },
  { id: 2, type: 'info', message: 'User checked out via WhatsApp', timestamp: '2025-07-01 10:05' },
];

const ErrorLogs = () => {
  const [logs, setLogs] = useState(mockLogs);
  const handleClear = () => setLogs([]);
  return (
    <AdminLayout>
      <Card className="max-w-2xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>Error Logs & Debug</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={handleClear} className="mb-4">Clear Logs</Button>
          <div className="space-y-2">
            {logs.length === 0 ? <div className="text-muted-foreground">No logs.</div> : logs.map(log => (
              <div key={log.id} className={`p-2 rounded ${log.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`}>
                <div className="text-xs">[{log.timestamp}]</div>
                <div>{log.message}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default ErrorLogs;
