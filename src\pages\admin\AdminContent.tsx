// Inline editable field for text and color
import React from 'react';

interface InlineEditableFieldProps {
  value: string;
  type: 'text' | 'color';
  onSave: (val: string) => Promise<void>;
  ariaLabel: string;
}

const InlineEditableField: React.FC<InlineEditableFieldProps> = ({ value, type, onSave, ariaLabel }) => {
  const [inlineValue, setInlineValue] = useState(value);
  const [inlineEditing, setInlineEditing] = useState(false);
  const [inlineLoading, setInlineLoading] = useState(false);

  const handleInlineSave = async () => {
    setInlineLoading(true);
    await onSave(inlineValue);
    setInlineEditing(false);
    setInlineLoading(false);
  };
  const handleInlineCancel = () => {
    setInlineEditing(false);
    setInlineValue(value);
  };

  if (inlineEditing) {
    return (
      <div className="flex items-center gap-2 w-full">
        {type === 'text' ? (
          <Input
            value={inlineValue}
            onChange={e => setInlineValue(e.target.value)}
            className="w-full max-w-xs"
            autoFocus
            onKeyDown={e => {
              if (e.key === 'Enter') handleInlineSave();
              if (e.key === 'Escape') handleInlineCancel();
            }}
            aria-label={ariaLabel}
          />
        ) : (
          <Input
            type="color"
            value={inlineValue}
            onChange={e => setInlineValue(e.target.value)}
            aria-label={ariaLabel}
          />
        )}
        <Button size="sm" onClick={handleInlineSave} disabled={inlineLoading} aria-label="Save inline edit">
          {inlineLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
        </Button>
        <Button size="sm" variant="outline" onClick={handleInlineCancel} aria-label="Cancel inline edit">Cancel</Button>
      </div>
    );
  }

  if (type === 'color') {
    return (
      <div className="flex items-center gap-2">
        <div
          className="w-6 h-6 rounded border color-swatch cursor-pointer"
          data-color={value}
          title={`Color: ${value}`}
          onClick={() => setInlineEditing(true)}
          tabIndex={0}
          aria-label={ariaLabel}
          role="button"
          onKeyDown={e => { if (e.key === 'Enter') setInlineEditing(true); }}
        />
        <span className="text-sm cursor-pointer underline decoration-dotted decoration-1" onClick={() => setInlineEditing(true)} tabIndex={0} role="button" aria-label={ariaLabel}>{value}</span>
      </div>
    );
  }
  // type === 'text'
  return (
    <span className="text-sm line-clamp-2 cursor-pointer underline decoration-dotted decoration-1" onClick={() => setInlineEditing(true)} tabIndex={0} role="button" aria-label={ariaLabel}>{value}</span>
  );
};
import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Select as ShadSelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import { supabase, Tour } from '@/lib/supabase';
import { Link } from 'react-router-dom';
import { Edit, Trash2, Plus, Image as ImageIcon, Palette, Globe, Settings, Star, Eye, Video, FileText, Loader2, Link2, Save } from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { toast } from 'sonner';

interface ContentItem {
  id: string;
  type: 'text' | 'image' | 'iframe' | 'background' | 'color' | 'setting';
  key: string;
  value: string;
  description: string;
  category: string;
  updated_at: string;
}

const defaultContent: ContentItem[] = [
  // Hero Section Content
  { id: '1', type: 'text', key: 'hero_title', value: 'Create Stunning Virtual Tours', description: 'Main hero section title', category: 'hero', updated_at: new Date().toISOString() },
  { id: '2', type: 'text', key: 'hero_subtitle', value: 'Transform any space into an immersive 360° experience that captivates your audience and drives business growth.', description: 'Hero section subtitle', category: 'hero', updated_at: new Date().toISOString() },
  { id: '3', type: 'image', key: 'hero_background', value: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80', description: 'Hero section background image', category: 'hero', updated_at: new Date().toISOString() },
  // About Section Content
  { id: '4', type: 'image', key: 'about_image', value: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80', description: 'About section image', category: 'about', updated_at: new Date().toISOString() },
  // Services Section Content
  { id: '5', type: 'image', key: 'services_image', value: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80', description: 'Services section image', category: 'services', updated_at: new Date().toISOString() },
  // Showcase Section Content
  { id: '6', type: 'image', key: 'showcase_image', value: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80', description: 'Showcase section image', category: 'showcase', updated_at: new Date().toISOString() },
  // Add more image blocks for every section as needed...
  // Site Settings
  { id: '7', type: 'text', key: 'site_name', value: 'VirtualRealTour', description: 'Website name', category: 'site', updated_at: new Date().toISOString() },
  { id: '8', type: 'text', key: 'site_description', value: 'Professional 360° Virtual Tours in Nigeria', description: 'Site meta description', category: 'site', updated_at: new Date().toISOString() },
  { id: '9', type: 'color', key: 'primary_color', value: '#3b82f6', description: 'Primary brand color', category: 'theme', updated_at: new Date().toISOString() },
  // Footer Content
  { id: '10', type: 'text', key: 'footer_description', value: 'Nigeria\'s premier virtual tour platform. Creating immersive experiences that drive business growth.', description: 'Footer description text', category: 'footer', updated_at: new Date().toISOString() },
  { id: '11', type: 'text', key: 'footer_copyright', value: '© 2024 VirtualRealTour. All rights reserved.', description: 'Footer copyright text', category: 'footer', updated_at: new Date().toISOString() }
];

const dynamicPlacements = [
  { key: 'featured_tour', label: 'Featured Tour', description: 'Tour displayed in the featured section' },
  { key: 'demo_tour', label: 'Demo Tour', description: 'Tour displayed as the demo on homepage/welcome' },
  { key: 'showcase_tour', label: 'Showcase Tour', description: 'Tour displayed in the showcase section' },
];

const AdminContent = () => {
  // Content export/import
  const handleExport = () => {
    const dataStr = JSON.stringify(content, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vrt-content-export-${new Date().toISOString().slice(0, 10)}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Content exported as JSON');
  };

  const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const imported = JSON.parse(event.target?.result as string);
        if (!Array.isArray(imported)) throw new Error('Invalid format');
        setContent(imported);
        toast.success('Content imported successfully');
      } catch (err) {
        toast.error('Failed to import: Invalid JSON format');
      }
    };
    reader.readAsText(file);
    e.target.value = '';
  };
  const importInputRef = useRef<HTMLInputElement>(null);
  // Advanced filtering state
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Bulk select logic
  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
  };
  const handleSelectAll = (ids: string[]) => {
    if (selectAll) {
      setSelectedItems([]);
      setSelectAll(false);
    } else {
      setSelectedItems(ids);
      setSelectAll(true);
    }
  };
  const handleBulkDelete = () => {
    if (selectedItems.length === 0) return;
    if (!window.confirm(`Delete ${selectedItems.length} selected items? This cannot be undone.`)) return;
    setContent(prev => prev.filter(c => !selectedItems.includes(c.id)));
    setSelectedItems([]);
    setSelectAll(false);
    toast.success('Selected items deleted successfully');
  };
  const [content, setContent] = useState<ContentItem[]>(defaultContent);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [newItem, setNewItem] = useState<Partial<ContentItem>>({ type: 'text', category: 'general' });
  const [isSaving, setIsSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Dynamic placements state (in real app, fetch from DB)
  const [placements, setPlacements] = useState({
    featured_tour: '',
    demo_tour: '',
    showcase_tour: '',
  });

  // Fetch all tours for dynamic placements
  const { data: tours, isLoading: loadingTours, isError: errorTours } = useQuery({
    queryKey: ['all-tours'],
    queryFn: async () => {
      const { data, error } = await supabase.from('tours').select('id, title, slug, status').order('created_at', { ascending: false });
      if (error) throw error;
      return data as Pick<Tour, 'id' | 'title' | 'slug' | 'status'>[];
    },
  });

  // Group content by category
  const contentByCategory = content.reduce((acc, item) => {
    if (!acc[item.category]) acc[item.category] = [];
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, ContentItem[]>);

  const handleSaveItem = async (item: ContentItem) => {
    setIsSaving(true);
    setTimeout(() => {
      setContent(prev => prev.map(c => c.id === item.id ? { ...item, updated_at: new Date().toISOString() } : c));
      toast.success('Content updated successfully');
      setEditingItem(null);
      setIsSaving(false);
    }, 400);
  };

  const handleDeleteItem = async (id: string) => {
    setContent(prev => prev.filter(c => c.id !== id));
    toast.success('Content deleted successfully');
  };

  const handleAddItem = async () => {
    if (!newItem.key || !newItem.value) {
      toast.error('Please fill in all required fields');
      return;
    }
    const item: ContentItem = {
      id: Date.now().toString(),
      type: newItem.type || 'text',
      key: newItem.key,
      value: newItem.value,
      description: newItem.description || '',
      category: newItem.category || 'general',
      updated_at: new Date().toISOString()
    };
    setContent(prev => [...prev, item]);
    setNewItem({ type: 'text', category: 'general' });
    toast.success('Content added successfully');
  };

  const handleImageUpload = async (file: File, itemId: string) => {
    const imageUrl = URL.createObjectURL(file);
    setContent(prev => prev.map(c => c.id === itemId ? { ...c, value: imageUrl, updated_at: new Date().toISOString() } : c));
    toast.success('Image uploaded successfully');
  };

  const handlePlacementChange = (key: string, value: string) => {
    setPlacements(prev => ({ ...prev, [key]: value }));
    toast.success('Placement updated');
  };

  const handleTypeSwitch = (item: ContentItem, newType: 'image' | 'iframe') => {
    setContent(prev => prev.map(c => c.id === item.id ? { ...c, type: newType, value: '', updated_at: new Date().toISOString() } : c));
    setEditingItem({ ...item, type: newType, value: '' });
  };

  const renderImageOrIframeEditor = (item: ContentItem) => (
    <div className="space-y-2">
      <Label>Type</Label>
      <ShadSelect value={item.type} onValueChange={val => handleTypeSwitch(item, val as 'image' | 'iframe')}>
        <SelectTrigger><SelectValue /></SelectTrigger>
        <SelectContent>
          <SelectItem value="image">Image</SelectItem>
          <SelectItem value="iframe">Iframe</SelectItem>
        </SelectContent>
      </ShadSelect>
      {item.type === 'image' ? (
        <div className="space-y-2">
          <Label>Image URL</Label>
          <Input value={editingItem?.value || ''} onChange={e => setEditingItem({ ...item, value: e.target.value })} />
          <Button onClick={() => fileInputRef.current?.click()} variant="outline" size="sm" aria-label="Upload image file" title="Upload image file"><ImageIcon className="w-4 h-4" /> Upload</Button>
          <input ref={fileInputRef} type="file" accept="image/*" className="hidden" title="Upload image file" aria-label="Upload image file" onChange={e => { const file = e.target.files?.[0]; if (file) handleImageUpload(file, item.id); }} />
        </div>
      ) : (
        <div className="space-y-2">
          <Label>Iframe URL</Label>
          <Input value={editingItem?.value || ''} onChange={e => setEditingItem({ ...item, value: e.target.value })} />
        </div>
      )}
    </div>
  );

  // Inline editing for text and color
  const renderContentEditor = (item: ContentItem) => {
    const isEditing = editingItem?.id === item.id;
    // Audit trail: relative time
    const getRelativeTime = (date: string) => {
      const now = new Date();
      const updated = new Date(date);
      const diff = Math.floor((now.getTime() - updated.getTime()) / 1000);
      if (diff < 60) return 'just now';
      if (diff < 3600) return `${Math.floor(diff / 60)} min ago`;
      if (diff < 86400) return `${Math.floor(diff / 3600)} hr ago`;
      return updated.toLocaleDateString();
    };

    if (isEditing) {
      return (
        <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
          <div className="space-y-2">
            <Label htmlFor={`edit-${item.id}`}>Value</Label>
            {item.type === 'text' ? (
              <Textarea id={`edit-${item.id}`} value={editingItem.value} onChange={e => setEditingItem({ ...editingItem, value: e.target.value })} rows={3} />
            ) : item.type === 'color' ? (
              <Input id={`edit-${item.id}`} type="color" value={editingItem.value} onChange={e => setEditingItem({ ...editingItem, value: e.target.value })} />
            ) : (renderImageOrIframeEditor(item))}
          </div>
          <div className="space-y-2">
            <Label htmlFor={`desc-${item.id}`}>Description</Label>
            <Input id={`desc-${item.id}`} value={editingItem.description} onChange={e => setEditingItem({ ...editingItem, description: e.target.value })} />
          </div>
          <div className="flex gap-2">
            <Button onClick={() => handleSaveItem(editingItem)} disabled={isSaving} size="sm">
              {isSaving ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />} Save
            </Button>
            <Button onClick={() => setEditingItem(null)} variant="outline" size="sm">Cancel</Button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-between p-4 border rounded-lg group focus-within:ring-2 focus-within:ring-primary" tabIndex={0}>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Badge variant="outline" className="text-xs">{item.type}</Badge>
            <code className="text-xs bg-muted px-2 py-1 rounded">{item.key}</code>
          </div>
          <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
          {/* Audit trail: last updated */}
          <div className="text-xs text-muted-foreground mb-1">Last updated: {getRelativeTime(item.updated_at)} by Admin</div>
          {item.type === 'image' ? (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <ImageIcon className="w-4 h-4 text-blue-600" />
              </div>
              <img src={item.value} alt={item.key} className="w-16 h-16 object-cover rounded" />
              <span className="text-sm truncate">{item.value}</span>
            </div>
          ) : item.type === 'iframe' ? (
            <div className="flex items-center gap-2">
              <Link2 className="w-5 h-5 text-blue-600" />
              <span className="text-sm truncate">{item.value}</span>
            </div>
          ) : item.type === 'color' || item.type === 'text' ? (
            <InlineEditableField
              value={item.value}
              type={item.type as 'text' | 'color'}
              onSave={async (val) => handleSaveItem({ ...item, value: val })}
              ariaLabel={`Edit ${item.type} value for ${item.key}`}
            />
          ) : (
            <p className="text-sm line-clamp-2">{item.value}</p>
          )}
        </div>
        <div className="flex items-center gap-2 ml-4">
          {(item.type === 'image' || item.type === 'iframe') && (
            <Button
              onClick={() => setEditingItem(item)}
              variant="ghost"
              size="icon"
              aria-label={`Edit content item ${item.key}`}
              title={`Edit content item ${item.key}`}
              className="rounded-full border border-muted hover:bg-accent focus:ring-2 focus:ring-primary focus:outline-none ml-1 transition-colors duration-150"
              tabIndex={0}
              data-testid={`edit-btn-${item.key}`}
            >
              <Edit className="w-4 h-4" aria-hidden="true" />
            </Button>
          )}
          <Button
            onClick={() => {
              if (window.confirm(`Are you sure you want to delete '${item.key}'? This action cannot be undone.`)) {
                handleDeleteItem(item.id);
              }
            }}
            variant="ghost"
            size="icon"
            className="rounded-full border border-red-200 text-red-600 hover:text-red-700 hover:bg-red-100 focus:ring-2 focus:ring-red-400 focus:outline-none ml-1 transition-colors duration-150"
            aria-label={`Delete content item ${item.key}`}
            title={`Delete content item ${item.key}`}
            tabIndex={0}
            data-testid={`delete-btn-${item.key}`}
          >
            <Trash2 className="w-4 h-4" aria-hidden="true" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-2 md:px-0">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Site Management</h1>
            <p className="text-muted-foreground">Manage all website content, placements, and settings from one place.</p>
          </div>
        </div>
        <Tabs defaultValue="content" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5" aria-label="Content management tabs">
            <TabsTrigger value="content" aria-label="Content Tab">Content</TabsTrigger>
            <TabsTrigger value="images" aria-label="Images and Iframes Tab">Images/Iframes</TabsTrigger>
            <TabsTrigger value="theme" aria-label="Theme Tab">Theme</TabsTrigger>
            <TabsTrigger value="placements" aria-label="Placements Tab">Placements</TabsTrigger>
            <TabsTrigger value="add" aria-label="Add New Content Tab">Add New</TabsTrigger>
          </TabsList>
          <TabsContent value="content" className="space-y-6">
          {/* Export/Import controls */}
          <div className="flex gap-2 mb-2">
            <Button size="sm" variant="outline" onClick={handleExport} aria-label="Export content as JSON">Export JSON</Button>
            <Button size="sm" variant="outline" onClick={() => importInputRef.current?.click()} aria-label="Import content from JSON">Import JSON</Button>
            <input
              ref={importInputRef}
              type="file"
              accept="application/json"
              className="hidden"
              onChange={handleImport}
              aria-label="Import content JSON file"
            />
          </div>
          {/* Filtering controls */}
          <div className="flex flex-wrap gap-2 mb-4 items-center">
            <label htmlFor="filter-category" className="text-xs font-medium">Category:</label>
            <select
              id="filter-category"
              value={filterCategory}
              onChange={e => setFilterCategory(e.target.value)}
              className="border rounded px-2 py-1 text-xs"
              aria-label="Filter by category"
            >
              <option value="all">All</option>
              {Object.keys(contentByCategory).map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
            <label htmlFor="filter-type" className="text-xs font-medium ml-2">Type:</label>
            <select
              id="filter-type"
              value={filterType}
              onChange={e => setFilterType(e.target.value)}
              className="border rounded px-2 py-1 text-xs"
              aria-label="Filter by type"
            >
              <option value="all">All</option>
              <option value="text">Text</option>
              <option value="image">Image</option>
              <option value="iframe">Iframe</option>
              <option value="color">Color</option>
            </select>
            <label htmlFor="search-term" className="text-xs font-medium ml-2">Search:</label>
            <input
              id="search-term"
              type="text"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="border rounded px-2 py-1 text-xs"
              placeholder="Search..."
              aria-label="Search content"
            />
          </div>
            {Object.entries(contentByCategory)
              .filter(([category]) => filterCategory === 'all' || category === filterCategory)
              .map(([category, items]) => {
                let filtered = items;
                if (filterType !== 'all') filtered = filtered.filter(item => item.type === filterType);
                if (searchTerm) filtered = filtered.filter(item =>
                  item.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  item.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  item.description.toLowerCase().includes(searchTerm.toLowerCase())
                );
                if (filtered.length === 0) return null;
                const allIds = filtered.map(i => i.id);
                return (
                  <Card key={category}>
                    <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-4">
                      <div className="flex items-center gap-2">
                        <CardTitle className="capitalize flex items-center gap-2">
                          {category === 'hero' && <Globe className="w-5 h-5" />}
                          {category === 'site' && <Settings className="w-5 h-5" />}
                          {category === 'theme' && <Palette className="w-5 h-5" />}
                          {category} Content
                        </CardTitle>
                      </div>
                      <div className="flex items-center gap-2 mt-2 md:mt-0 flex-wrap">
                        <input
                          type="checkbox"
                          aria-label={`Select all items in ${category}`}
                          checked={allIds.every(id => selectedItems.includes(id)) && allIds.length > 0}
                          onChange={() => handleSelectAll(allIds)}
                          tabIndex={0}
                          className="accent-primary focus:ring-2 focus:ring-primary focus:outline-none rounded border border-muted"
                        />
                        <span className="text-xs" id={`select-all-label-${category}`}>Select All</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            if (selectedItems.length === 0) return;
                            if (window.confirm(`Are you sure you want to delete all selected items in '${category}'? This action cannot be undone.`)) {
                              handleBulkDelete();
                            }
                          }}
                          disabled={selectedItems.length === 0}
                          aria-label="Delete all selected items in this category"
                          title="Delete all selected items in this category"
                          className="rounded-full border border-red-200 text-red-600 hover:text-red-700 hover:bg-red-50 focus:ring-2 focus:ring-red-400 focus:outline-none ml-1"
                        >
                          <Trash2 className="w-4 h-4 mr-1" aria-hidden="true" /> Bulk Delete
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {filtered.map(item => (
                        <div key={item.id} className="flex items-center gap-2 flex-col sm:flex-row">
                          <input
                            type="checkbox"
                            aria-label={`Select content item ${item.key}`}
                            checked={selectedItems.includes(item.id)}
                            onChange={() => handleSelectItem(item.id)}
                            tabIndex={0}
                            className="accent-primary focus:ring-2 focus:ring-primary focus:outline-none rounded border border-muted"
                          />
                          <div className="flex-1 w-full">{renderContentEditor(item)}</div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                );
              })}
          </TabsContent>
          <TabsContent value="images" className="space-y-6">
            {Object.entries(contentByCategory).map(([category, items]) => {
              const imageOrIframeItems = items.filter(item => item.type === 'image' || item.type === 'iframe');
              if (imageOrIframeItems.length === 0) return null;
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize flex items-center gap-2"><ImageIcon className="w-5 h-5" />{category} Images/Iframes</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {imageOrIframeItems.map(item => (<div key={item.id}>{renderContentEditor(item)}</div>))}
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
          <TabsContent value="theme" className="space-y-6">
            {Object.entries(contentByCategory).map(([category, items]) => {
              const themeItems = items.filter(item => item.type === 'color');
              if (themeItems.length === 0) return null;
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize flex items-center gap-2"><Palette className="w-5 h-5" />{category} Colors</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {themeItems.map(item => (<div key={item.id}>{renderContentEditor(item)}</div>))}
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
          <TabsContent value="placements" className="space-y-6">
            <Card>
              <CardHeader><CardTitle>Dynamic Content Placements</CardTitle></CardHeader>
              <CardContent className="space-y-4">
                {errorTours ? (
                  <div className="text-red-600">Failed to load tours. Please try again later.</div>
                ) : loadingTours ? (
                  <div className="flex items-center gap-2"><Loader2 className="animate-spin w-5 h-5 text-muted-foreground" /> Loading tours...</div>
                ) : !tours || tours.length === 0 ? (
                  <div className="text-muted-foreground">No tours available. Please create a tour first.</div>
                ) : (
                  dynamicPlacements.map(placement => (
                    <div key={placement.key} className="flex flex-col md:flex-row md:items-center gap-3 md:gap-6 border rounded-lg p-4">
                      <div className="flex-1">
                        <div className="font-semibold mb-1">{placement.label}</div>
                        <div className="text-xs text-muted-foreground mb-2">{placement.description}</div>
                      </div>
                      <div className="w-full md:w-80">
                        <ShadSelect value={placements[placement.key] || ''} onValueChange={val => handlePlacementChange(placement.key, val)}>
                          <SelectTrigger><SelectValue placeholder="Select a tour" /></SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            {tours.filter(t => t.status === 'published').map(tour => (
                              <SelectItem key={tour.id} value={tour.id}>{tour.title}</SelectItem>
                            ))}
                          </SelectContent>
                        </ShadSelect>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="add" className="space-y-6">
            <Card>
              <CardHeader><CardTitle className="flex items-center gap-2"><Plus className="w-5 h-5" />Add New Content</CardTitle></CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newKey">Key</Label>
                    <Input id="newKey" value={newItem.key || ''} onChange={e => setNewItem({ ...newItem, key: e.target.value })} placeholder="e.g., hero_title" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newCategory">Category</Label>
                    <Input id="newCategory" value={newItem.category || ''} onChange={e => setNewItem({ ...newItem, category: e.target.value })} placeholder="e.g., hero, site, theme" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newType">Type</Label>
                  <ShadSelect value={newItem.type || 'text'} onValueChange={val => setNewItem({ ...newItem, type: val as ContentItem['type'] })}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text</SelectItem>
                      <SelectItem value="image">Image</SelectItem>
                      <SelectItem value="iframe">Iframe</SelectItem>
                      <SelectItem value="color">Color</SelectItem>
                    </SelectContent>
                  </ShadSelect>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newValue">Value</Label>
                  <Textarea id="newValue" value={newItem.value || ''} onChange={e => setNewItem({ ...newItem, value: e.target.value })} placeholder="Content value..." rows={3} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newDescription">Description</Label>
                  <Input id="newDescription" value={newItem.description || ''} onChange={e => setNewItem({ ...newItem, description: e.target.value })} placeholder="Description of this content..." />
                </div>
                <Button onClick={handleAddItem} className="w-full"><Plus className="w-4 h-4 mr-2" />Add Content</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        <div className="flex flex-wrap gap-4 mt-8">
          <Link to="/admin/tours"><Button variant="outline"><Star className="w-4 h-4 mr-2" />Manage Tours</Button></Link>
          <Link to="/admin/media"><Button variant="outline"><ImageIcon className="w-4 h-4 mr-2" />Manage Media</Button></Link>
          <Link to="/admin/wordpress"><Button variant="outline"><Globe className="w-4 h-4 mr-2" />WordPress Content</Button></Link>
          <Link to="/admin/settings"><Button variant="outline"><Settings className="w-4 h-4 mr-2" />Site Settings</Button></Link>
          <Link to="/admin/woo-api"><Button variant="outline"><FileText className="w-4 h-4 mr-2" />Woo API Management</Button></Link>
          <Link to="/admin/whatsapp"><Button variant="outline"><ImageIcon className="w-4 h-4 mr-2" />WhatsApp Checkout Settings</Button></Link>
          <Link to="/admin/overlay-preview"><Button variant="outline"><Eye className="w-4 h-4 mr-2" />Overlay/Glass UI Preview</Button></Link>
          <Link to="/admin/cart"><Button variant="outline"><span className="inline-block align-middle"><svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13l-1.35 2.7A1 1 0 007 17h10a1 1 0 00.95-.68l3.24-7.24A1 1 0 0020 7H7.42" /></svg></span>Cart Management</Button></Link>
          <Link to="/admin/error-logs"><Button variant="outline"><FileText className="w-4 h-4 mr-2" />Error Logs & Debug</Button></Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminContent;
