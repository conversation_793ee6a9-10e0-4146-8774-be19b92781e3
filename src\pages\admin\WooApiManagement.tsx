import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import AdminLayout from '@/layouts/AdminLayout';
import { fetchProductById } from '@/api/wooApi';

// WooCommerce Product type (partial, extend as needed)
interface WooProduct {
  id: number;
  name: string;
  price: string;
  status: string;
}

const WooApiManagement = () => {
  const [productId, setProductId] = useState('');
  const [product, setProduct] = useState<null | WooProduct>(null);
  const [loading, setLoading] = useState(false);

  const handleFetchProduct = async () => {
    if (!productId) {
      toast.error('Enter a product ID');
      return;
    }
    setLoading(true);
    try {
      const data = await fetchProductById(productId);
      // Only pick the fields we care about for type safety
      const safeProduct: WooProduct = {
        id: data.id,
        name: data.name,
        price: data.price,
        status: data.status,
      };
      setProduct(safeProduct);
      toast.success('Product fetched successfully');
    } catch (e) {
      toast.error('Failed to fetch product');
      setProduct(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <Card className="max-w-xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>WooCommerce API Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="productId">Product ID</Label>
            <Input id="productId" value={productId} onChange={e => setProductId(e.target.value)} placeholder="Enter WooCommerce Product ID" />
          </div>
          <Button onClick={handleFetchProduct} disabled={loading} className="w-full">{loading ? 'Fetching...' : 'Fetch Product'}</Button>
          {product && (
            <div className="mt-4 p-4 border rounded bg-muted">
              <div className="font-semibold">{product.name}</div>
              <div>Price: {product.price}</div>
              <div>Status: {product.status}</div>
              <div>ID: {product.id}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default WooApiManagement;
