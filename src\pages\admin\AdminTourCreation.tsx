/**
 * Admin Tour Creation Page
 * Configuration and management for tour creation services
 */

import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Settings, Zap, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

const AdminTourCreation = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Tour Creation Services</h1>
          <p className="text-muted-foreground">Manage and configure tour creation platforms</p>
        </div>

        {/* Service Status Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Service Status</span>
            </CardTitle>
            <CardDescription>
              Current status of tour creation services
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">Custom Tour Service</p>
                    <p className="text-sm text-muted-foreground">Native VRT platform</p>
                  </div>
                </div>
                <Badge variant="default">Active</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Zap className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">External Integrations</p>
                    <p className="text-sm text-muted-foreground">Third-party platforms</p>
                  </div>
                </div>
                <Badge variant="secondary">Available</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tour Creation Features */}
        <Card>
          <CardHeader>
            <CardTitle>Available Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Tour creation services are fully operational and ready for use.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">✅ Active Features:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Native VRT tour creation with Photo Sphere Viewer</li>
                  <li>360° image upload and processing</li>
                  <li>External tour embedding (Panoee, Metaport, etc.)</li>
                  <li>Tour management and publishing</li>
                  <li>Mobile-responsive tour viewer</li>
                  <li>SEO-friendly tour URLs with slugs</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">🚀 Quick Actions:</h4>
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href="/admin/tours" className="flex items-center space-x-2">
                      <span>Manage Tours</span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href="/showcase" className="flex items-center space-x-2">
                      <span>View Showcase</span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Integration Information */}
        <Card>
          <CardHeader>
            <CardTitle>Platform Integration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Supported Platforms:</h4>
                <div className="grid gap-3 md:grid-cols-2">
                  <div className="p-3 border rounded-lg">
                    <p className="font-medium">Native VRT Platform</p>
                    <p className="text-sm text-muted-foreground">Built-in tour creation with Photo Sphere Viewer</p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <p className="font-medium">External Embeds</p>
                    <p className="text-sm text-muted-foreground">Support for Panoee, Metaport, and other platforms</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminTourCreation;
