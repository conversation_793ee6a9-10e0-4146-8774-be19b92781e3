import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import AdminLayout from '@/layouts/AdminLayout';

const WhatsAppCheckoutSettings = () => {
  const [number, setNumber] = useState('');
  const [message, setMessage] = useState('');

  const handleSave = () => {
    // Persist settings to backend or local storage
    toast.success('WhatsApp checkout settings saved');
  };

  return (
    <AdminLayout>
      <Card className="max-w-xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>WhatsApp Checkout Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="wa-number">Default WhatsApp Number</Label>
            <Input id="wa-number" value={number} onChange={e => setNumber(e.target.value)} placeholder="e.g. 2348012345678" />
          </div>
          <div>
            <Label htmlFor="wa-message">Default Message</Label>
            <Input id="wa-message" value={message} onChange={e => setMessage(e.target.value)} placeholder="e.g. Hello, I'm interested in..." />
          </div>
          <Button onClick={handleSave} className="w-full">Save Settings</Button>
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default WhatsAppCheckoutSettings;
