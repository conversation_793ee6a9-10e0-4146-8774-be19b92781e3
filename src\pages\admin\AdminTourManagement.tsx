/**
 * Admin Tour Management Page
 * Complete tour management with commerce integration
 * Enhanced admin interface for tour creation and management
 */

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Search, 
  Filter, 
  MapPin, 
  Eye, 
  Edit, 
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Grid,
  List,
  Download,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import EnhancedTourCreation from '@/components/admin/EnhancedTourCreation';
import TourCard from '@/components/admin/TourCard';
import TourAnalytics from '@/components/admin/TourAnalytics';

interface Tour {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  status: 'draft' | 'published';
  thumbnail_url?: string;
  business_name?: string;
  created_at: string;
  updated_at: string;
  views_count?: number;
  views?: number; // Alternative field name
  commerce_enabled?: boolean;
  vendor_id?: string;
  slug?: string;
  vendor?: {
    id: string;
    name: string;
  };
}

const AdminTourManagement = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showCreateTour, setShowCreateTour] = useState(false);

  // Fetch tours
  const { data: tours = [], isLoading, refetch } = useQuery({
    queryKey: ['admin-tours', searchTerm, statusFilter],
    queryFn: async () => {
      let query = supabase
        .from('tours')
        .select(`
          *,
          vendor:vendors(id, name)
        `)
        .order('created_at', { ascending: false });

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;
      if (error) {
        console.error('Error fetching tours:', error);
        // If vendor join fails, try without vendor data
        const fallbackQuery = supabase
          .from('tours')
          .select('*')
          .order('created_at', { ascending: false });

        if (searchTerm) {
          fallbackQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
        }
        if (statusFilter !== 'all') {
          fallbackQuery.eq('status', statusFilter);
        }

        const { data: fallbackData, error: fallbackError } = await fallbackQuery;
        if (fallbackError) throw fallbackError;
        return fallbackData;
      }
      return data;
    },
    enabled: !!user,
  });

  // Calculate stats
  const stats = {
    total: tours.length,
    published: tours.filter(t => t.status === 'published').length,
    draft: tours.filter(t => t.status === 'draft').length,
    withCommerce: tours.filter(t => t.commerce_enabled).length,
    totalViews: tours.reduce((sum, t) => sum + (t.views_count || t.views || 0), 0)
  };

  const handleCreateTour = (_tourId: string) => {
    setShowCreateTour(false);
    refetch();
  };

  if (showCreateTour) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold">Create New Tour</h1>
              <p className="text-muted-foreground">Set up a new virtual tour with commerce integration</p>
            </div>
            <Button variant="outline" onClick={() => setShowCreateTour(false)}>
              Back to Tours
            </Button>
          </div>
          
          <EnhancedTourCreation 
            onSuccess={handleCreateTour}
            onCancel={() => setShowCreateTour(false)}
          />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Tour Management</h1>
            <p className="text-muted-foreground">Manage virtual tours and commerce integration</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setShowCreateTour(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Tour
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Tours</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <MapPin className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Published</p>
                  <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                </div>
                <Eye className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Draft</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.draft}</p>
                </div>
                <Edit className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">With Commerce</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.withCommerce}</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Views</p>
                  <p className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-indigo-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tours">All Tours</TabsTrigger>
            <TabsTrigger value="commerce">Commerce</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Tours</CardTitle>
                  <CardDescription>Latest created tours</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {tours.slice(0, 5).map(tour => (
                      <div key={tour.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{tour.title}</h4>
                          <p className="text-sm text-muted-foreground">{tour.location}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={tour.status === 'published' ? 'default' : 'secondary'}>
                            {tour.status}
                          </Badge>
                          {tour.commerce_enabled && (
                            <Badge variant="outline">
                              <ShoppingCart className="w-3 h-3 mr-1" />
                              Commerce
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common tasks</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setShowCreateTour(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Tour
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setActiveTab('commerce')}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Manage Commerce
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setActiveTab('analytics')}
                  >
                    <BarChart3 className="w-4 h-4 mr-2" />
                    View Analytics
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Tours Tab */}
          <TabsContent value="tours" className="space-y-6">
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search tours..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
                aria-label="Filter tours by status"
                title="Filter tours by status"
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
              
              <div className="flex gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Tours Grid/List */}
            {isLoading ? (
              <div className="text-center py-8">Loading tours...</div>
            ) : tours.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <MapPin className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No tours found</h3>
                  <p className="text-muted-foreground mb-4">Create your first virtual tour to get started</p>
                  <Button onClick={() => setShowCreateTour(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Tour
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className={cn(
                viewMode === 'grid' 
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                  : "space-y-4"
              )}>
                {tours.map(tour => (
                  <TourCard 
                    key={tour.id} 
                    tour={tour} 
                    viewMode={viewMode}
                    onUpdate={refetch}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Commerce Tab */}
          <TabsContent value="commerce" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Commerce Overview</CardTitle>
                <CardDescription>Tours with e-commerce integration</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.withCommerce}</div>
                    <div className="text-sm text-muted-foreground">Commerce Enabled</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {tours.filter(t => t.vendor_id).length}
                    </div>
                    <div className="text-sm text-muted-foreground">With Vendors</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {stats.total - stats.withCommerce}
                    </div>
                    <div className="text-sm text-muted-foreground">Tour Only</div>
                  </div>
                </div>

                <div className="space-y-4">
                  {tours.filter(t => t.commerce_enabled).map(tour => (
                    <div key={tour.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{tour.title}</h4>
                        <p className="text-sm text-muted-foreground">{tour.business_name || tour.location}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="default">Commerce Active</Badge>
                        <Button size="sm" variant="outline">
                          <Settings className="w-4 h-4 mr-1" />
                          Manage
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <TourAnalytics tours={tours} />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminTourManagement;
