import AdminWordPress from "./pages/admin/AdminWordPress";
              <Route
                path="/admin/wordpress"
                element={
                  <AuthGuard requireAdmin>
                    <AdminWordPress />
                  </AuthGuard>
                }
              />

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import { AuthGuard } from "@/components/AuthGuard";
import { ThemeProvider } from "@/components/ThemeProvider";
import { initializeTheme } from "@/lib/theme";
import { useEffect } from "react";
import Index from "./pages/Index";
import Welcome from "./pages/Welcome";
import Dashboard from "./pages/Dashboard";
import AdminDashboard from "./pages/AdminDashboard";
import AdminTours from "./pages/admin/AdminTours";
import AdminTourManagement from "./pages/admin/AdminTourManagement";
import AdminVendors from "./pages/admin/AdminVendors";
import AdminUsers from "./pages/admin/AdminUsers";
import AdminContent from "./pages/admin/AdminContent";
import WooApiManagement from "./pages/admin/WooApiManagement";
import WhatsAppCheckoutSettings from "./pages/admin/WhatsAppCheckoutSettings";
import OverlayPreview from "./pages/admin/OverlayPreview";
import CartManagement from "./pages/admin/CartManagement";
import ErrorLogs from "./pages/admin/ErrorLogs";
import AdminProfile from "./pages/admin/AdminProfile";
import AdminFeatured from "./pages/admin/AdminFeatured";
import AdminDemo from "./pages/admin/AdminDemo";
import AdminTourEditor from "./pages/admin/AdminTourEditor";
import AdminMedia from "./pages/admin/AdminMedia";
import AdminAnalytics from "./pages/admin/AdminAnalytics";
import AdminSettings from "./pages/admin/AdminSettings";
import AdminDiagnostics from "./pages/admin/AdminDiagnostics";
import Services from "./pages/Services";
import Showcase from "./pages/Showcase";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Auth from "./pages/Auth";
import TourView from "./pages/TourView";
import WPVRTourPage from "./pages/WPVRTourPage";
import WooCommerceTest from "./pages/WooCommerceTest";
import UserTourEditor from "./pages/UserTourEditor";
import NotFound from "./pages/NotFound";

// Commerce Pages
import ProductDetails from "./pages/ProductDetails";
import VendorDashboard from "./pages/VendorDashboard";
import VendorProducts from "./pages/VendorProducts";
import AdminCommerce from "./pages/admin/AdminCommerce";
import OrderTracking from "./pages/OrderTracking";

// Floating Components
import FloatingWhatsAppWidget from "./components/commerce/FloatingWhatsAppWidget";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Initialize theme system
initializeTheme();

const App = () => {

  return (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light" storageKey="vrt-ui-theme">
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/welcome" element={<Welcome />} />
              <Route path="/services" element={<Services />} />
              <Route path="/showcase" element={<Showcase />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/tour/:slug" element={<TourView />} />
              <Route path="/wpvr-tour/:slug" element={<WPVRTourPage />} />
              <Route path="/woocommerce-test" element={<AuthGuard requireAdmin><WooCommerceTest /></AuthGuard>} />

              {/* Commerce Routes */}
              <Route path="/product/:id" element={<ProductDetails />} />
              <Route path="/order/:id" element={<OrderTracking />} />

              <Route
                path="/dashboard"
                element={
                  <AuthGuard>
                    <Dashboard />
                  </AuthGuard>
                }
              />
              <Route
                path="/dashboard/tours/:tourId/edit"
                element={
                  <AuthGuard>
                    <UserTourEditor />
                  </AuthGuard>
                }
              />

              {/* Vendor Routes */}
              <Route
                path="/vendor"
                element={
                  <AuthGuard>
                    <VendorDashboard />
                  </AuthGuard>
                }
              />
              <Route
                path="/vendor/products"
                element={
                  <AuthGuard>
                    <VendorProducts />
                  </AuthGuard>
                }
              />
              <Route 
                path="/admin" 
                element={
                  <AuthGuard requireAdmin>
                    <AdminDashboard />
                  </AuthGuard>
                } 
              />
              <Route
                path="/admin/tours"
                element={
                  <AuthGuard requireAdmin>
                    <AdminTours />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/tour-management"
                element={
                  <AuthGuard requireAdmin>
                    <AdminTourManagement />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/tours/:tourId/edit"
                element={
                  <AuthGuard requireAdmin>
                    <AdminTourEditor />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/vendors"
                element={
                  <AuthGuard requireAdmin>
                    <AdminVendors />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/users"
                element={
                  <AuthGuard requireAdmin>
                    <AdminUsers />
                  </AuthGuard>
                }
              />
              <Route 
                path="/admin/content" 
                element={
                  <AuthGuard requireAdmin>
                    <AdminContent />
                  </AuthGuard>
                } 
              />
              <Route
                path="/admin/woo-api"
                element={
                  <AuthGuard requireAdmin>
                    <WooApiManagement />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/whatsapp"
                element={
                  <AuthGuard requireAdmin>
                    <WhatsAppCheckoutSettings />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/overlay-preview"
                element={
                  <AuthGuard requireAdmin>
                    <OverlayPreview />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/cart"
                element={
                  <AuthGuard requireAdmin>
                    <CartManagement />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/error-logs"
                element={
                  <AuthGuard requireAdmin>
                    <ErrorLogs />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/featured"
                element={
                  <AuthGuard requireAdmin>
                    <AdminFeatured />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/demo"
                element={
                  <AuthGuard requireAdmin>
                    <AdminDemo />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/media"
                element={
                  <AuthGuard requireAdmin>
                    <AdminMedia />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/analytics"
                element={
                  <AuthGuard requireAdmin>
                    <AdminAnalytics />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/settings"
                element={
                  <AuthGuard requireAdmin>
                    <AdminSettings />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/profile"
                element={
                  <AuthGuard requireAdmin>
                    <AdminProfile />
                  </AuthGuard>
                }
              />
              <Route
                path="/admin/commerce"
                element={
                  <AuthGuard requireAdmin>
                    <AdminCommerce />
                  </AuthGuard>
                }
              />

              <Route
                path="/admin/diagnostics"
                element={
                  <AuthGuard requireAdmin>
                    <AdminDiagnostics />
                  </AuthGuard>
                }
              />

              <Route path="*" element={<NotFound />} />
            </Routes>

            {/* Floating Components - Available on all pages */}
            <FloatingWhatsAppWidget />
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
  );
};

export default App;
