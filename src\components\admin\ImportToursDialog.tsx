import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { sampleTours } from '@/data/sampleTours';
import { supabase } from '@/lib/supabase';
import { createTourSlug } from '@/lib/slugUtils';
import { Download, Check, AlertCircle, Loader2, Eye, RefreshCw, FileText, ExternalLink, XCircle, AlertTriangle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import ResponsiveAdminDialog from './ResponsiveAdminDialog';
import ResponsiveAdminForm from './ResponsiveAdminForm';
import ResponsiveAdminCard from './ResponsiveAdminCard';
// import TourImportPreview, { type TourPreviewData } from './TourImportPreview'; // Temporarily disabled
import { validateTourUrlEnhanced } from '@/utils/tourImportHelper';

// Temporary type definition
interface TourPreviewData {
  title: string;
  description: string;
  category: string;
  location: string;
  embed_url: string;
  embed_type: string;
  business_type?: string;
}

interface ImportToursDialogProps {
  onImportComplete?: () => void;
}

const ImportToursDialog = ({ onImportComplete }: ImportToursDialogProps) => {
  const [open, setOpen] = useState(false);
  const [selectedTours, setSelectedTours] = useState<string[]>([]);
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState<{
    success: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const [importUrl, setImportUrl] = useState('');
  const [fetchedTours, setFetchedTours] = useState<typeof sampleTours | null>(null);

  // Enhanced state for preview and validation
  const [previewTour, setPreviewTour] = useState<TourPreviewData | null>(null);
  const [validationResults, setValidationResults] = useState<Map<string, any>>(new Map());
  const [isValidating, setIsValidating] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [currentImportStep, setCurrentImportStep] = useState('');

  const { user } = useAuth();
  const queryClient = useQueryClient();

  const handleTourSelection = (tourId: string, checked: boolean) => {
    if (checked) {
      setSelectedTours(prev => [...prev, tourId]);
    } else {
      setSelectedTours(prev => prev.filter(id => id !== tourId));
    }
  };

  const handleSelectAll = () => {
    if (selectedTours.length === sampleTours.length) {
      setSelectedTours([]);
    } else {
      setSelectedTours(sampleTours.map(tour => tour.id));
    }
  };

  const importTour = async (tour: typeof sampleTours[0]) => {
    if (!user?.id) throw new Error('User not authenticated');

    // Generate unique slug
    const baseSlug = createTourSlug(tour.title);
    const { data: existingTours } = await supabase
      .from('tours')
      .select('slug')
      .like('slug', `${baseSlug}%`);

    const existingSlugs = existingTours?.map(t => t.slug).filter(Boolean) || [];
    let uniqueSlug = baseSlug;
    let counter = 1;

    while (existingSlugs.includes(uniqueSlug)) {
      uniqueSlug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Insert tour (default to draft status)
    const { data, error } = await supabase
      .from('tours')
      .insert({
        title: tour.title,
        description: tour.description,
        category: tour.category,
        location: tour.location,
        embed_url: tour.embed_url,
        embed_type: tour.embed_type,
        business_type: tour.business_type,
        slug: uniqueSlug,
        user_id: user.id,
        status: 'draft',
        featured: false,
        scenes_count: 1,
        views: 0
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  };

  const handleImport = async () => {
    if (!user?.id) {
      toast.error("Please log in to import tours.");
      return;
    }

    if (selectedTours.length === 0) {
      toast.error("Please select at least one tour to import.");
      return;
    }

    setImporting(true);
    setImportProgress(0);
    const results = { success: 0, failed: 0, errors: [] as string[] };

    // Use fetchedTours if available, otherwise sampleTours
    const toursSource = fetchedTours || sampleTours;
    const toursToImport = toursSource.filter(tour => selectedTours.includes(tour.id));

    for (let i = 0; i < toursToImport.length; i++) {
      const tour = toursToImport[i];
      setCurrentImportStep(`Importing "${tour.title}" (${i + 1}/${toursToImport.length})`);

      try {
        // Validate before import if not already validated
        if (!validationResults.has(tour.id)) {
          setCurrentImportStep(`Validating "${tour.title}"...`);
          const validation = await validateTourUrlEnhanced(tour.embed_url);

          if (!validation.isValid || !validation.isAccessible) {
            throw new Error(`Validation failed: ${validation.error || 'URL is not accessible'}`);
          }

          if (validation.warnings.length > 0) {
            console.warn(`Import warnings for ${tour.title}:`, validation.warnings);
          }
        }

        setCurrentImportStep(`Importing "${tour.title}"...`);
        await importTour(tour);
        results.success++;

        // Update progress
        setImportProgress(((i + 1) / toursToImport.length) * 100);

        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        results.failed++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.errors.push(`${tour.title}: ${errorMessage}`);
        console.error(`Failed to import ${tour.title}:`, error);

        // Continue with next tour even if one fails
        setImportProgress(((i + 1) / toursToImport.length) * 100);
      }
    }

    setImportResults(results);
    setImporting(false);
    setImportProgress(0);
    setCurrentImportStep('');

    // Refresh tours data
    queryClient.invalidateQueries({ queryKey: ['tours'] });
    queryClient.invalidateQueries({ queryKey: ['admin-dashboard-tours'] });

    if (results.success > 0) {
      toast.success(`Successfully imported ${results.success} tour${results.success > 1 ? 's' : ''}.`);
      if (onImportComplete) {
        onImportComplete();
      }
    }

    if (results.failed > 0) {
      toast.error(`${results.failed} tour${results.failed > 1 ? 's' : ''} failed to import. Check the details below.`);
    }
  };

  const resetDialog = () => {
    setSelectedTours([]);
    setImportResults(null);
    setImporting(false);
    setImportUrl('');
    setFetchedTours(null);
    setPreviewTour(null);
    setValidationResults(new Map());
    setImportProgress(0);
    setCurrentImportStep('');
  };

  // Enhanced validation for selected tours
  const validateSelectedTours = async () => {
    const toursSource = fetchedTours || sampleTours;
    const toursToValidate = toursSource.filter(tour => selectedTours.includes(tour.id));

    if (toursToValidate.length === 0) return;

    setIsValidating(true);
    const results = new Map();

    for (let i = 0; i < toursToValidate.length; i++) {
      const tour = toursToValidate[i];
      try {
        const validation = await validateTourUrlEnhanced(tour.embed_url);
        results.set(tour.id, validation);
      } catch (error) {
        results.set(tour.id, {
          isValid: false,
          isAccessible: false,
          error: error instanceof Error ? error.message : 'Validation failed',
          warnings: ['Failed to validate URL']
        });
      }

      // Update progress
      setImportProgress(((i + 1) / toursToValidate.length) * 100);
    }

    setValidationResults(results);
    setIsValidating(false);
    setImportProgress(0);
  };

  // Preview a single tour before import
  const handlePreviewTour = (tour: any) => {
    const tourData: TourPreviewData = {
      title: tour.title,
      description: tour.description,
      category: tour.category,
      location: tour.location,
      embed_url: tour.embed_url,
      embed_type: tour.embed_type,
      business_type: tour.business_type
    };
    setPreviewTour(tourData);
  };

  // Import single tour from preview
  const handleImportFromPreview = async (tourData: TourPreviewData) => {
    if (!user) return;

    setImporting(true);
    setCurrentImportStep('Importing tour...');

    try {
      await importTour(tourData);
      toast.success(`Successfully imported "${tourData.title}".`);
      setPreviewTour(null);
      if (onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Import failed');
    } finally {
      setImporting(false);
      setCurrentImportStep('');
    }
  };

  // Fetch tours from custom URL
  const handleFetchFromUrl = async () => {
    if (!importUrl) return;
    setImporting(true);
    try {
      const res = await fetch(importUrl);
      if (!res.ok) throw new Error('Failed to fetch tours from URL');
      const data = await res.json();
      if (!Array.isArray(data)) throw new Error('Invalid tours format: expected an array');
      setFetchedTours(data);
      setSelectedTours([]);
      toast.success(`Tours loaded from URL: ${data.length} tours loaded.`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to fetch tours');
      setFetchedTours(null);
    }
    setImporting(false);
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        resetDialog();
      }
    }}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Import Curated Tours
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full max-w-screen-sm sm:max-w-lg md:max-w-2xl p-0 overflow-hidden">
        <DialogHeader className="px-4 pt-4">
          <DialogTitle>Import Curated Tours</DialogTitle>
          <DialogDescription>
            Select high-quality tours to add to your platform. All tours are verified to display properly without source branding.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 pb-4 pt-2 w-full">
        {previewTour ? (
          <div className="p-4 text-center">
            <p>Preview functionality temporarily disabled</p>
            <Button onClick={() => setPreviewTour(null)} className="mt-2">Back</Button>
          </div>
        ) : importResults ? (
          <div className="space-y-4 w-full">
            <div className="flex items-center gap-2">
              <Check className="h-5 w-5 text-green-500" />
              <span className="font-medium">Import Complete</span>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full">
              <Card className="p-4 flex-1">
                <div className="text-sm text-green-600 font-semibold">Successful</div>
                <div className="text-2xl font-bold text-green-600">{importResults.success}</div>
              </Card>
              <Card className="p-4 flex-1">
                <div className="text-sm text-red-600 font-semibold">Failed</div>
                <div className="text-2xl font-bold text-red-600">{importResults.failed}</div>
              </Card>
            </div>
            {importResults.errors.length > 0 && (
              <Card className="p-4">
                <div className="text-sm flex items-center gap-2 font-semibold text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  Import Errors
                </div>
                <ScrollArea className="h-32">
                  <div className="space-y-1">
                    {importResults.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-600">
                        {error}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </Card>
            )}
            <div className="flex flex-col sm:flex-row justify-end gap-2 w-full">
              <Button variant="outline" onClick={() => setOpen(false)} className="w-full sm:w-auto">
                Close
              </Button>
              <Button onClick={resetDialog} className="w-full sm:w-auto">
                Import More Tours
              </Button>
            </div>
          </div>
        ) : (
          <form onSubmit={e => { e.preventDefault(); handleImport(); }} className="space-y-4 w-full">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
              <div className="flex-1 flex flex-col gap-2 sm:flex-row sm:items-center w-full">
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Checkbox
                    id="select-all"
                    checked={selectedTours.length === (fetchedTours ? fetchedTours.length : sampleTours.length)}
                    onCheckedChange={handleSelectAll}
                  />
                  <label htmlFor="select-all" className="text-sm font-medium">
                    Select All ({fetchedTours ? fetchedTours.length : sampleTours.length} tours)
                  </label>
                </div>
                <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                  <input
                    type="text"
                    placeholder="Paste import URL (JSON) and click Load"
                    className="border rounded px-2 py-1 text-sm w-full sm:w-80"
                    value={importUrl}
                    onChange={e => setImportUrl(e.target.value)}
                    disabled={importing}
                  />
                  <Button size="sm" variant="outline" onClick={handleFetchFromUrl} disabled={!importUrl || importing} className="w-full sm:w-auto">
                    Load from URL
                  </Button>
                </div>
              </div>
              <Badge variant="secondary" className="mt-2 sm:mt-0">
                {selectedTours.length} selected
              </Badge>
            </div>

            {/* Validation and Progress Section */}
            {(isValidating || importing) && (
              <Card className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm font-medium">
                      {isValidating ? 'Validating tours...' : 'Importing tours...'}
                    </span>
                  </div>

                  {importProgress > 0 && (
                    <div className="space-y-2">
                      <Progress value={importProgress} className="w-full" />
                      <div className="text-xs text-muted-foreground">
                        {Math.round(importProgress)}% complete
                      </div>
                    </div>
                  )}

                  {currentImportStep && (
                    <div className="text-xs text-muted-foreground">
                      {currentImportStep}
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* Validation Controls */}
            {selectedTours.length > 0 && !isValidating && !importing && (
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={validateSelectedTours}
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Validate Selected
                </Button>

                {validationResults.size > 0 && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <FileText className="h-3 w-3" />
                    {Array.from(validationResults.values()).filter(v => v.isValid && v.isAccessible).length} valid,
                    {Array.from(validationResults.values()).filter(v => v.warnings?.length > 0).length} warnings,
                    {Array.from(validationResults.values()).filter(v => !v.isValid || !v.isAccessible).length} issues
                  </div>
                )}
              </div>
            )}
            <ScrollArea className="h-96 max-w-full overflow-x-auto">
              <div className="flex flex-col gap-3 w-full">
                {(fetchedTours || sampleTours).map((tour) => (
                  <Card key={tour.id} className="p-4 w-full max-w-full">
                    <div className="flex flex-col sm:flex-row items-start gap-3 w-full max-w-full">
                      <Checkbox
                        id={tour.id}
                        checked={selectedTours.includes(tour.id)}
                        onCheckedChange={(checked) => handleTourSelection(tour.id, !!checked)}
                        className="mt-1"
                      />
                      <div className="flex-1 space-y-1 min-w-0">
                        <div className="flex flex-wrap items-center gap-2 min-w-0">
                          <h4 className="font-medium text-sm truncate max-w-[180px] sm:max-w-[240px]">{tour.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {tour.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {tour.source_platform}
                          </Badge>
                          {/* Validation Status Badge */}
                          {validationResults.has(tour.id) && (() => {
                            const validation = validationResults.get(tour.id);
                            if (validation.isValid && validation.isAccessible) {
                              return <Badge className="text-xs bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" />Valid</Badge>;
                            } else if (validation.warnings?.length > 0) {
                              return <Badge className="text-xs bg-yellow-100 text-yellow-800 border-yellow-200"><AlertTriangle className="h-3 w-3 mr-1" />Warning</Badge>;
                            } else {
                              return <Badge className="text-xs bg-red-100 text-red-800 border-red-200"><XCircle className="h-3 w-3 mr-1" />Issues</Badge>;
                            }
                          })()}
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2 break-words max-w-full">
                          {tour.description}
                        </p>
                        <div className="text-xs text-muted-foreground truncate">
                          📍 {tour.location} • {tour.business_type}
                        </div>

                        {/* Validation Details */}
                        {validationResults.has(tour.id) && (() => {
                          const validation = validationResults.get(tour.id);
                          if (validation.warnings?.length > 0 || validation.error) {
                            return (
                              <div className="text-xs space-y-1">
                                {validation.error && (
                                  <div className="text-red-600 flex items-center gap-1">
                                    <XCircle className="h-3 w-3" />
                                    {validation.error}
                                  </div>
                                )}
                                {validation.warnings?.map((warning: string, idx: number) => (
                                  <div key={idx} className="text-yellow-600 flex items-center gap-1">
                                    <AlertTriangle className="h-3 w-3" />
                                    {warning}
                                  </div>
                                ))}
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreviewTour(tour)}
                          className="text-xs h-7 px-2"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(tour.embed_url, '_blank')}
                          className="text-xs h-7 px-2"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
            <div className="flex flex-col sm:flex-row justify-end gap-2 w-full">
              <Button variant="outline" onClick={() => setOpen(false)} className="w-full sm:w-auto">
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={selectedTours.length === 0 || importing}
                className="gap-2 w-full sm:w-auto"
              >
                {importing && <Loader2 className="h-4 w-4 animate-spin" />}
                Import {selectedTours.length} Tour{selectedTours.length !== 1 ? 's' : ''}
              </Button>
            </div>
          </form>
        )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImportToursDialog;
