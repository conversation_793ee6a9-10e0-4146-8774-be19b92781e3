import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, RefreshCw, Download } from 'lucide-react';
import AdminLayout from '@/layouts/AdminLayout';
import { toast } from 'sonner';

import { fetchWPVRTours } from '@/api/wpvrApi';
// Import WPVRTour type for type safety
import type { WPVRTour } from './AdminTours';

const AdminWordPress = () => {
  const { data: wpvrTours = [], isLoading, refetch } = useQuery({
    queryKey: ['admin-wordpress-tours'],
    queryFn: fetchWPVRTours,
  });

  return (
    <AdminLayout>
      <div className="space-y-6 p-4 md:p-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">WordPress Content</h1>
            <p className="text-muted-foreground">Manage all imported and synced WPVR/WordPress tours and products</p>
          </div>
          <Button onClick={() => refetch()} variant="outline" size="sm"><RefreshCw className="w-4 h-4 mr-2" />Refresh</Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>WPVR Tours & Products</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">Loading WordPress content...</div>
            ) : wpvrTours.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No WordPress content found.</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {wpvrTours.map((tour: WPVRTour) => (
                  <Card key={tour.id} className="flex flex-col">
                    <CardContent className="flex-1 flex flex-col gap-2 p-4">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-400">WPVR</Badge>
                        <span className="text-xs text-muted-foreground">ID: {tour.id}</span>
                        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-800 border-gray-400">{tour.status || 'draft'}</Badge>
                      </div>
                      <div className="font-semibold text-base truncate">{tour.title}</div>
                      <div className="text-xs text-muted-foreground line-clamp-2">{tour.description}</div>
                      {tour.thumbnail && (
                        <img src={tour.thumbnail} alt={tour.title} className="w-full h-32 object-cover rounded" />
                      )}
                      <div className="flex gap-2 mt-2">
                        <Button size="sm" variant="outline" onClick={() => {
                          // Try to open a sensible URL for the tour if available
                          const url = (typeof tour === 'object' && 'url' in tour && typeof tour.url === 'string')
                            ? tour.url
                            : (typeof tour === 'object' && 'link' in tour && typeof (tour as { link?: string }).link === 'string')
                              ? (tour as { link: string }).link
                              : `/wpvr-tour/${tour.id}`;
                          window.open(url, '_blank');
                        }}><Eye className="w-4 h-4 mr-1" />View</Button>
                        <Button size="sm" variant="outline" onClick={() => toast.info('Download not implemented yet')}><Download className="w-4 h-4 mr-1" />Download</Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminWordPress;
