import React from 'react';
import ProductHotspot, { ProductHotspotProps } from './ProductHotspot';

export interface TourEmbedProps {
  tourUrl: string; // Your custom slug/URL, never the source
  hotspots?: ProductHotspotProps[];
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
  iframeTitle?: string;
}

const TourEmbed: React.FC<TourEmbedProps> = ({
  tourUrl,
  hotspots = [],
  width = '100%',
  height = '600px',
  className = '',
  style = {},
  iframeTitle = 'Virtual Tour',
}) => {
  return (
    <div className={`tour-embed relative ${className}`} style={{ width, height, ...style }}>
      <iframe
        src={tourUrl}
        title={iframeTitle}
        width="100%"
        height="100%"
        frameBorder={0}
        allow="fullscreen; accelerometer; gyroscope; magnetometer; vr; xr; xr-spatial-tracking; autoplay; camera; microphone"
        allowFullScreen
        style={{ border: 'none', position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
        sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
      />
      {hotspots.map((hotspot, idx) => (
        <ProductHotspot key={hotspot.product?.id || idx} {...hotspot} />
      ))}
    </div>
  );
};

export default TourEmbed;
