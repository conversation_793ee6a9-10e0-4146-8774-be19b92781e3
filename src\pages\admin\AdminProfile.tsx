import { useAuth } from '@/hooks/useAuth';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import AdminLayout from '@/layouts/AdminLayout';
import { useState } from 'react';

const AdminProfile = () => {
  const { user, profile } = useAuth();
  const [fullName, setFullName] = useState(profile?.full_name || '');
  const [email] = useState(user?.email || '');
  // Add more fields as needed

  // Placeholder for update logic
  const handleUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement update logic (call supabase, show toast, etc.)
    alert('Profile updated! (not yet implemented)');
  };

  return (
    <AdminLayout>
      <div className="max-w-xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Admin Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <form className="space-y-4" onSubmit={handleUpdate}>
              <div>
                <label className="block text-sm font-medium mb-1">Full Name</label>
                <Input value={fullName} onChange={e => setFullName(e.target.value)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input value={email} disabled />
              </div>
              {/* Add more fields as needed */}
              <Button type="submit" className="w-full mt-4">Update Profile</Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminProfile;
