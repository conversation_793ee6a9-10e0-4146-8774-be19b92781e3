
import { useQuery } from '@tanstack/react-query';
import { supabase, Tour, Profile } from '@/lib/supabase';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminStatsCards from '@/components/admin/AdminStatsCards';
import AdminCharts from '@/components/admin/AdminCharts';
import AdminRecentActivity from '@/components/admin/AdminRecentActivity';
import ImportToursDialog from '@/components/admin/ImportToursDialog';

const AdminDashboard = () => {
  // Fetch dashboard data
  const { data: tours = [] } = useQuery({
    queryKey: ['admin-dashboard-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Tour[];
    },
  });

  const { data: users = [] } = useQuery({
    queryKey: ['admin-dashboard-users'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Profile[];
    },
  });

  const { data: analytics = [] } = useQuery({
    queryKey: ['admin-dashboard-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tour_analytics')
        .select('*')
        .order('viewed_at', { ascending: false })
        .limit(100);
      
      if (error) return [];
      return data;
    },
  });

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Quick Actions Bar */}
        <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 flex flex-wrap gap-2 py-3 px-2 rounded-lg shadow md:shadow-none mb-2">
          <ImportToursDialog onImportComplete={() => window.location.reload()} />
          {/* Add more quick actions here as needed */}
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold">Tour-First Admin Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">Manage your virtual tour platform with tour-centric insights.</p>
          </div>
        </div>

        {/* Collapsible Dashboard Sections for Mobile UX */}
        <div className="space-y-4">
          <details open className="rounded-lg border bg-white dark:bg-gray-950 shadow-sm">
            <summary className="cursor-pointer px-4 py-3 font-semibold text-lg flex items-center gap-2">
              📊 Key Metrics
            </summary>
            <div className="p-2 sm:p-4">
              <AdminStatsCards tours={tours} users={users} analytics={analytics} />
            </div>
          </details>

          <details open className="rounded-lg border bg-white dark:bg-gray-950 shadow-sm">
            <summary className="cursor-pointer px-4 py-3 font-semibold text-lg flex items-center gap-2">
              📈 Charts & Analytics
            </summary>
            <div className="p-2 sm:p-4 overflow-x-auto">
              <AdminCharts tours={tours} analytics={analytics} />
            </div>
          </details>

          <details open className="rounded-lg border bg-white dark:bg-gray-950 shadow-sm">
            <summary className="cursor-pointer px-4 py-3 font-semibold text-lg flex items-center gap-2">
              🕒 Recent Activity
            </summary>
            <div className="p-2 sm:p-4 overflow-x-auto">
              <AdminRecentActivity tours={tours} users={users} />
            </div>
          </details>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
