import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Upload, 
  Globe, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  RefreshCw,
  FileText,
  ExternalLink,
  Settings
} from 'lucide-react';
import AdminLayout from '@/layouts/AdminLayout';
import ImportToursDialog from '@/components/admin/ImportToursDialog';
// import BrokenLinkChecker from '@/components/admin/BrokenLinkChecker'; // Temporarily disabled
import ApplicationDiagnostics from '@/components/admin/ApplicationDiagnostics';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

const AdminTourImport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('import');

  // Get tour statistics
  const { data: tourStats } = useQuery({
    queryKey: ['admin-tour-stats'],
    queryFn: async () => {
      const { data: tours, error } = await supabase
        .from('tours')
        .select('id, status, creation_method, embed_url, hotspots');

      if (error) throw error;

      const stats = {
        total: tours?.length || 0,
        published: tours?.filter(t => t.status === 'published').length || 0,
        draft: tours?.filter(t => t.status === 'draft').length || 0,
        imported: tours?.filter(t => t.creation_method === 'import').length || 0,
        withHotspots: tours?.filter(t => t.hotspots && Array.isArray(t.hotspots) && t.hotspots.length > 0).length || 0,
        withEmbedUrl: tours?.filter(t => t.embed_url).length || 0
      };

      return stats;
    }
  });

  return (
    <AdminLayout>
      <div className="space-y-6 p-4 md:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Tour Import & Management</h1>
            <p className="text-muted-foreground">
              Import tours from external sources and monitor link health
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        {tourStats && (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold">{tourStats.total}</div>
                  <div className="text-xs text-muted-foreground">Total Tours</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold text-green-600">{tourStats.published}</div>
                  <div className="text-xs text-muted-foreground">Published</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold text-yellow-600">{tourStats.draft}</div>
                  <div className="text-xs text-muted-foreground">Draft</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold text-blue-600">{tourStats.imported}</div>
                  <div className="text-xs text-muted-foreground">Imported</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold text-purple-600">{tourStats.withHotspots}</div>
                  <div className="text-xs text-muted-foreground">With Hotspots</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4 sm:pt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold text-indigo-600">{tourStats.withEmbedUrl}</div>
                  <div className="text-xs text-muted-foreground">With Embed</div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-1 sm:grid-cols-4 gap-1 sm:gap-0">
            <TabsTrigger value="import" className="flex items-center gap-2 text-xs sm:text-sm">
              <Download className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Import Tours</span>
              <span className="sm:hidden">Import</span>
            </TabsTrigger>
            <TabsTrigger value="health" className="flex items-center gap-2 text-xs sm:text-sm">
              <Globe className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Link Health</span>
              <span className="sm:hidden">Health</span>
            </TabsTrigger>
            <TabsTrigger value="diagnostics" className="flex items-center gap-2 text-xs sm:text-sm">
              <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Diagnostics</span>
              <span className="sm:hidden">Diag</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2 text-xs sm:text-sm">
              <Settings className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Settings</span>
              <span className="sm:hidden">Config</span>
            </TabsTrigger>
          </TabsList>

          {/* Import Tours Tab */}
          <TabsContent value="import" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Import Sources */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Import Sources
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-sm sm:text-base">Curated Tours</div>
                          <div className="text-xs sm:text-sm text-muted-foreground">
                            High-quality verified tours
                          </div>
                        </div>
                      </div>
                      <div className="w-full sm:w-auto">
                        <ImportToursDialog />
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <ExternalLink className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium text-sm sm:text-base">Custom URL</div>
                          <div className="text-xs sm:text-sm text-muted-foreground">
                            Import from JSON endpoint
                          </div>
                        </div>
                      </div>
                      <div className="w-full sm:w-auto">
                        <Button variant="outline" disabled className="w-full sm:w-auto">
                          <Upload className="h-4 w-4 mr-2" />
                          Coming Soon
                        </Button>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Globe className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-medium text-sm sm:text-base">Platform Integration</div>
                          <div className="text-xs sm:text-sm text-muted-foreground">
                            CloudPano, Matterport, etc.
                          </div>
                        </div>
                      </div>
                      <div className="w-full sm:w-auto">
                        <Button variant="outline" disabled className="w-full sm:w-auto">
                          <Settings className="h-4 w-4 mr-2" />
                          Configure
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Import Guidelines */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Import Guidelines
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Best Practices:</strong>
                        <ul className="mt-2 space-y-1 text-sm">
                          <li>• Always preview tours before importing</li>
                          <li>• Validate URLs for accessibility</li>
                          <li>• Check embed compatibility</li>
                          <li>• Review content for quality</li>
                        </ul>
                      </AlertDescription>
                    </Alert>

                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Supported Platforms:</strong>
                        <div className="mt-2 flex flex-wrap gap-1">
                          <Badge variant="outline">Panoee</Badge>
                          <Badge variant="outline">TourMkr</Badge>
                          <Badge variant="outline">CloudPano</Badge>
                          <Badge variant="outline">CommonNinja</Badge>
                          <Badge variant="outline">Matterport</Badge>
                        </div>
                      </AlertDescription>
                    </Alert>

                    <Alert>
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Import Limitations:</strong>
                        <ul className="mt-2 space-y-1 text-sm">
                          <li>• Tours imported as drafts by default</li>
                          <li>• Manual review required for publishing</li>
                          <li>• Some platforms may have display restrictions</li>
                          <li>• Hotspots need manual configuration</li>
                        </ul>
                      </AlertDescription>
                    </Alert>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Link Health Tab */}
          <TabsContent value="health" className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p>Link health monitoring temporarily disabled</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Diagnostics Tab */}
          <TabsContent value="diagnostics" className="space-y-6">
            <ApplicationDiagnostics />
          </TabsContent>

          {/* Import Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Import Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Settings className="h-4 w-4" />
                  <AlertDescription>
                    Import settings and platform integrations will be available in a future update.
                    Currently, all imports use default settings with manual review required.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminTourImport;
