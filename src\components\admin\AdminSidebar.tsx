import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  MapPin,
  Star,
  FileText,
  Settings,
  BarChart3,
  Shield,
  Globe,
  Image,
  UserCog,
  User,
  Activity,
  Sparkles,
  Edit
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';

// Consolidated navigation for clarity and shadcn-admin style
const adminNavGroups = [
  {
    label: 'Dashboard',
    items: [
      { title: 'Dashboard', url: '/admin', icon: LayoutDashboard, description: 'Overview & Analytics' },
    ]
  },
  {
    label: 'Content Management',
    items: [
      { title: 'Content', url: '/admin/content', icon: FileText, description: 'Website content, homepage, features, and sections' },
      { title: 'Tours', url: '/admin/tours', icon: MapPin, description: 'Create & manage all tours' },
      { title: 'Featured Content', url: '/admin/featured', icon: Star, description: 'Homepage & featured tours' },
      { title: 'Demo Tour', url: '/admin/demo', icon: Globe, description: 'Set homepage demo tour' },
      { title: 'Media Library', url: '/admin/media', icon: Image, description: 'Manage images & media' },
    ]
  },
  {
    label: 'Commerce',
    items: [
      { title: 'Tour Management', url: '/admin/tour-management', icon: Settings, description: 'Advanced tour management & commerce' },
      { title: 'Vendors', url: '/admin/vendors', icon: Users, description: 'Manage businesses & clients' },
      { title: 'Commerce', url: '/admin/commerce', icon: Sparkles, description: 'Orders & transactions' },
    ]
  },
  {
    label: 'Users & Analytics',
    items: [
      { title: 'Users', url: '/admin/users', icon: UserCog, description: 'Platform user management' },
      { title: 'Analytics', url: '/admin/analytics', icon: BarChart3, description: 'Platform performance' },
    ]
  },
  {
    label: 'Settings',
    items: [
      { title: 'Settings', url: '/admin/settings', icon: Settings, description: 'Platform configuration' },
      { title: 'Diagnostics', url: '/admin/diagnostics', icon: Activity, description: 'System diagnostics' },
      { title: 'Profile', url: '/admin/profile', icon: User, description: 'Admin profile' },
    ]
  }
];

export function AdminSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    if (path === '/admin') {
      return currentPath === '/admin';
    }
    return currentPath.startsWith(path);
  };

  const getNavClassName = (path: string) => {
    return isActive(path)
      ? "bg-primary/10 text-primary border-r-2 border-primary font-medium"
      : "hover:bg-muted text-muted-foreground hover:text-foreground";
  };

  return (
    <Sidebar className="border-r bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60">
      <SidebarHeader className="border-b bg-background/95 px-4 py-3 md:px-6 md:py-5">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center rounded-xl overflow-hidden bg-primary/10 h-10 w-10 md:h-12 md:w-12">
            <img
              src="/lovable-uploads/vrt-logo-all.png"
              alt="VirtualRealTour Logo"
              className="object-cover h-6 w-6 md:h-8 md:w-8"
            />
          </div>
          {state === 'expanded' && (
            <div className="flex-1">
              <h2 className="font-semibold text-foreground text-base md:text-lg">VirtualRealTour</h2>
              <p className="text-muted-foreground text-xs md:text-sm">Admin Portal</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 py-4 bg-background/95">
        {adminNavGroups.map((group) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel className="px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {group.label}
            </SidebarGroupLabel>
            <SidebarGroupContent className="mt-2">
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild className="h-auto p-0">
                      <NavLink
                        to={item.url}
                        className={`flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 min-h-[2.25rem] ${getNavClassName(item.url)}`}
                      >
                        <item.icon className="h-4 w-4 flex-shrink-0" />
                        {state === 'expanded' && (
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium leading-5">{item.title}</div>
                            <div className={`text-xs leading-4 mt-0.5 ${isActive(item.url) ? 'text-primary' : 'text-muted-foreground'}`}>
                              {item.description}
                            </div>
                          </div>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </SidebarGroupLabel>
          <SidebarGroupContent className="mt-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/dashboard" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <User className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">User Dashboard</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <Globe className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">View Website</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t px-4 py-5 bg-background/95">
        {state === 'expanded' && (
          <div className="space-y-3">
            <div className="flex items-center gap-3 px-2 py-2 rounded-lg bg-muted">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <UserCog className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">{user?.email}</p>
                <p className="text-xs text-muted-foreground">Administrator</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={signOut}
              className="w-full justify-start"
            >
              Sign Out
            </Button>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
