import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Search, Plus, Eye, Edit, Trash2, Star, MoreHorizontal, Settings, Target, Save, ShoppingCart } from 'lucide-react';
// Remove useLocalState, use useState everywhere
import TourEmbed from '@/components/hotspot/TourEmbed';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { supabase, Tour } from '@/lib/supabase';
import { toast } from 'sonner';
import AdminLayout from '@/components/admin/AdminLayout';
import CreateTourModal from '@/components/admin/CreateTourModal';
import TourEditModal from '@/components/admin/TourEditModal';
import { fetchWooProducts } from '@/api/wooProductsApi';
import { fetchWPVRTours } from '@/api/wpvrApi';

// Types for external sources
interface WooProduct {
  id: number;
  name: string;
  description: string;
  status: string;
  images?: { src: string }[];
}

export interface WPVRTour {
  id: number;
  title: string;
  description: string;
  status?: string;
  thumbnail?: string;
}

const AdminTours = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [editingTour, setEditingTour] = useState<Tour | null>(null);


  // Fetch all sources in parallel
  const { data: supabaseTours = [], isLoading: loadingSupabase, refetch } = useQuery({
    queryKey: ['admin-tours-supabase'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .order('created_at', { ascending: false });
      if (error) throw error;
      return data as Tour[];
    },
  });

  const { data: wooProducts = [], isLoading: loadingWoo } = useQuery({
    queryKey: ['admin-tours-woo'],
    queryFn: fetchWooProducts,
  });

  const { data: wpvrTours = [], isLoading: loadingWPVR } = useQuery({
    queryKey: ['admin-tours-wpvr'],
    queryFn: fetchWPVRTours,
  });

  // Merge and label all sources, with clear source/verification and type-safe _raw
  const allTours: AllTour[] = [
    ...supabaseTours.map(t => ({ ...t, _source: 'Supabase' as const })),
    ...((wooProducts as WooProduct[]).map((p) => ({
      id: String(p.id),
      title: p.name,
      description: p.description,
      status: p.status === 'publish' ? 'published' : 'draft',
      thumbnail_url: p.images?.[0]?.src,
      _source: 'WooCommerce' as const,
      _verified: !!p.id && !!p.name,
      _raw: p as WooProduct
    }))),
    ...((wpvrTours as WPVRTour[]).map((w) => ({
      id: String(w.id),
      title: w.title,
      description: w.description,
      status: w.status || 'draft',
      thumbnail_url: w.thumbnail,
      _source: 'WPVR' as const,
      _verified: !!w.id && !!w.title,
      _raw: w as WPVRTour
    }))),
  ];

  // Enhanced filtering
  type AllTour =
    (Tour & { _source: 'Supabase' }) |
    ({
      id: string;
      title: string;
      description: string;
      status: string;
      thumbnail_url?: string;
      _source: 'WooCommerce';
      _verified: boolean;
      _raw: WooProduct;
    }) |
    ({
      id: string;
      title: string;
      description: string;
      status: string;
      thumbnail_url?: string;
      _source: 'WPVR';
      _verified: boolean;
      _raw: WPVRTour;
    });

  // --- Hotspot Management State ---
  type Hotspot = {
    id: string;
    x: number;
    y: number;
    label: string;
    product?: WooProduct | null;
  };
  const [hotspotTour, setHotspotTour] = useState<AllTour | null>(null);
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [showHotspotModal, setShowHotspotModal] = useState(false);
  const [hotspotSaving, setHotspotSaving] = useState(false);

  // Load hotspots from Supabase when opening modal
  const handleOpenHotspotModal = async (tour: AllTour) => {
    setHotspotTour(tour);
    setShowHotspotModal(true);
    if (isSupabaseTour(tour)) {
      // Load from DB
      const { data, error } = await supabase
        .from('tours')
        .select('hotspots')
        .eq('id', tour.id)
        .single();
      if (!error && data && Array.isArray(data.hotspots)) {
        setHotspots(data.hotspots);
      } else {
        setHotspots([]);
      }
    } else {
      setHotspots([]);
    }
  };

  // Save hotspots to Supabase
  const handleSaveHotspots = async () => {
    if (!hotspotTour || !isSupabaseTour(hotspotTour)) return;
    setHotspotSaving(true);
    const { error } = await supabase
      .from('tours')
      .update({ hotspots })
      .eq('id', hotspotTour.id);
    setHotspotSaving(false);
    if (error) {
      toast.error('Failed to save hotspots');
    } else {
      toast.success('Hotspots saved successfully');
      setShowHotspotModal(false);
      refetch();
    }
  };
  // Example: Add a hotspot at a random position
  const handleAddHotspot = () => {
    if (!hotspotTour) return;
    setHotspots(prev => [
      ...prev,
      {
        id: Date.now().toString(),
        x: Math.random() * 80 + 10,
        y: Math.random() * 80 + 10,
        label: `Hotspot ${prev.length + 1}`,
        product: null,
      },
    ]);
  };
  const handleRemoveHotspot = (id: string) => setHotspots(prev => prev.filter(h => h.id !== id));

  function isSupabaseTour(tour: AllTour): tour is Tour & { _source: 'Supabase' } {
    return tour._source === 'Supabase';
  }

  const filteredTours = allTours.filter(tour => {
    const matchesSearch = (tour.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tour.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || tour.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleToggleFeatured = async (tour: Tour) => {
    try {
      const { error } = await supabase
        .from('tours')
        .update({ featured: !tour.featured })
        .eq('id', tour.id);

      if (error) throw error;

      toast.success(`Tour ${tour.featured ? 'unfeatured' : 'featured'} successfully`);
      refetch();
    } catch (error) {
      console.error('Error updating tour:', error);
      toast.error('Failed to update tour');
    }
  };

  const handleStatusChange = async (tourId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('tours')
        .update({ status: newStatus })
        .eq('id', tourId);

      if (error) throw error;

      toast.success(`Tour ${newStatus} successfully`);
      refetch();
    } catch (error) {
      console.error('Error updating tour status:', error);
      toast.error('Failed to update tour status');
    }
  };

  const handleDeleteTour = async (tourId: string) => {
    if (!confirm('Are you sure you want to delete this tour? This action cannot be undone.')) return;

    try {
      const { error } = await supabase
        .from('tours')
        .delete()
        .eq('id', tourId);

      if (error) throw error;

      toast.success('Tour deleted successfully');
      refetch();
    } catch (error) {
      console.error('Error deleting tour:', error);
      toast.error('Failed to delete tour');
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      draft: 'bg-yellow-100 text-yellow-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge className={variants[status] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold">Tour Management Hub</h1>
            <p className="text-sm sm:text-base text-muted-foreground">Central hub for all tour creation, approval, and management</p>
          </div>
          <CreateTourModal onSuccess={() => refetch()} />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{allTours.length}</div>
                  <p className="text-sm text-muted-foreground">Total Tours</p>
                </div>
                <MapPin className="w-5 h-5 text-primary" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{allTours.filter(t => t.status === 'published').length}</div>
                  <p className="text-sm text-muted-foreground">Published</p>
                </div>
                <Eye className="w-5 h-5 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{allTours.filter(t => t.status === 'draft').length}</div>
                  <p className="text-sm text-muted-foreground">Drafts</p>
                </div>
                <Edit className="w-5 h-5 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Tours</CardTitle>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search tours..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredTours.length === 0 ? (
                <div className="text-center py-8">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No tours found</h3>
                  <p className="text-muted-foreground">Create your first virtual tour to get started.</p>
                </div>
              ) : (
                filteredTours.map((tour) => (
                  <div key={tour.id} className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center space-x-4 flex-1">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                        {tour.thumbnail_url ? (
                          <img
                            src={tour.thumbnail_url}
                            alt={tour.title}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <MapPin className="w-4 h-4 sm:w-6 sm:h-6 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-sm sm:text-base truncate">{tour.title}</h3>
                          {isSupabaseTour(tour) && tour.featured && <Star className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" />}
                        </div>
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{tour.description}</p>
                        <div className="flex flex-wrap items-center gap-2 mt-1">
                          {getStatusBadge(tour.status)}
                          {isSupabaseTour(tour) && tour.category && (
                            <Badge variant="outline" className="text-xs">{tour.category}</Badge>
                          )}
                          <Badge variant="outline" className="text-xs bg-gray-200 border-gray-400 text-gray-700">
                            {tour._source}
                          </Badge>
                          {!isSupabaseTour(tour) && (
                            <Badge variant="outline" className={`text-xs ${tour._verified ? 'bg-green-100 text-green-800 border-green-400' : 'bg-yellow-100 text-yellow-800 border-yellow-400'}`}>{tour._verified ? 'Verified' : 'Unverified'}</Badge>
                          )}
                          {isSupabaseTour(tour) && (
                            <>
                              {/* Embedded/Custom/Basic badge logic fallback for Supabase tours */}
                              {(() => {
                                // Defensive: check for embedded/custom/basic using optional chaining and unknown property access
                                const t = tour as unknown as { commonninja_embed_code?: string; commonninja_widget_id?: string; custom_scenes?: unknown[] };
                                if (typeof t.commonninja_embed_code === 'string' && t.commonninja_embed_code) {
                                  return <Badge variant="outline" className="text-xs text-blue-600 border-blue-600">Embedded</Badge>;
                                }
                                if (typeof t.commonninja_widget_id === 'string' && t.commonninja_widget_id) {
                                  return <Badge variant="outline" className="text-xs text-blue-600 border-blue-600">Embedded</Badge>;
                                }
                                if (Array.isArray(t.custom_scenes) && t.custom_scenes.length > 0) {
                                  return <Badge variant="outline" className="text-xs text-green-600 border-green-600">Custom</Badge>;
                                }
                                return <Badge variant="outline" className="text-xs text-gray-600 border-gray-600">Basic</Badge>;
                              })()}
                              <span className="text-xs text-muted-foreground">{('views' in tour && typeof tour.views === 'number') ? tour.views : 0} views</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between sm:justify-end gap-2 sm:space-x-2">
                    <div className="flex items-center gap-1 sm:gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleOpenHotspotModal(tour)}
                          className="h-8 w-auto px-2"
                          title="Manage Hotspots"
                        >
                          <Target className="w-4 h-4 mr-1" />Hotspots
                        </Button>
        {/* Hotspot Modal/Drawer */}
        {showHotspotModal && hotspotTour && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl p-6 relative">
              <button
                type="button"
                className="absolute top-2 right-2 text-gray-500 hover:text-black"
                onClick={() => setShowHotspotModal(false)}
                aria-label="Close Hotspot Manager"
              >
                ×
              </button>
              <h2 className="text-xl font-bold mb-2">Manage Hotspots for: {hotspotTour.title}</h2>
              <div className="mb-4">
                <TourEmbed
                  tourUrl={hotspotTour.thumbnail_url || ''}
                  hotspots={hotspots.map(h => ({
                    x: h.x,
                    y: h.y,
                    label: h.label,
                    product: h.product
                      ? {
                          id: String(h.product.id),
                          title: h.product.name || '',
                          price: 0,
                          image_url: h.product.images?.[0]?.src || '',
                          url: '',
                        }
                      : undefined,
                    icon: undefined,
                    color: '#3b82f6',
                    overlayStyle: 'glass',
                    style: { pointerEvents: 'none' },
                  }))}
                  height={320}
                  style={{ borderRadius: 12, overflow: 'hidden' }}
                />
              </div>
              <div className="flex gap-2 mb-4">
                <Button onClick={handleAddHotspot} variant="outline" size="sm"><Plus className="w-4 h-4 mr-1" />Add Hotspot</Button>
                <Button onClick={handleSaveHotspots} variant="default" size="sm" disabled={hotspotSaving}>
                  {hotspotSaving ? <span className="animate-spin mr-2">⏳</span> : <Save className="w-4 h-4 mr-1" />}Save Hotspots
                </Button>
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {hotspots.length === 0 ? (
                  <div className="text-muted-foreground text-sm">No hotspots yet.</div>
                ) : (
                  hotspots.map((h, idx) => (
                    <div key={h.id} className="flex flex-col sm:flex-row items-center gap-2 p-2 border rounded bg-muted/50">
                      <div className="flex items-center gap-2 flex-1">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <ShoppingCart className="w-3 h-3 text-white" />
                        </div>
                        <input
                          type="text"
                          value={h.label}
                          onChange={e => setHotspots(hs => hs.map(hh => hh.id === h.id ? { ...hh, label: e.target.value } : hh))}
                          className="border rounded px-2 py-1 text-xs w-28"
                          aria-label="Hotspot label"
                        />
                        <input
                          type="number"
                          value={h.x}
                          min={0}
                          max={100}
                          step={1}
                          onChange={e => setHotspots(hs => hs.map(hh => hh.id === h.id ? { ...hh, x: Number(e.target.value) } : hh))}
                          className="border rounded px-1 py-1 text-xs w-12"
                          aria-label="Hotspot X position"
                          title="X position (percent)"
                        />
                        <input
                          type="number"
                          value={h.y}
                          min={0}
                          max={100}
                          step={1}
                          onChange={e => setHotspots(hs => hs.map(hh => hh.id === h.id ? { ...hh, y: Number(e.target.value) } : hh))}
                          className="border rounded px-1 py-1 text-xs w-12"
                          aria-label="Hotspot Y position"
                          title="Y position (percent)"
                        />
                        <Select
                          value={h.product ? String(h.product.id) : ''}
                          onValueChange={val => setHotspots(hs => hs.map(hh => hh.id === h.id ? { ...hh, product: wooProducts.find(p => String(p.id) === val) || null } : hh))}
                        >
                          <SelectTrigger className="w-32 text-xs h-8">
                            <SelectValue placeholder="Link Product" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            {wooProducts.map((p: WooProduct) => (
                              <SelectItem key={p.id} value={String(p.id)}>{p.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <Button size="icon" variant="ghost" onClick={() => handleRemoveHotspot(h.id)} aria-label="Remove Hotspot"><Trash2 className="w-4 h-4 text-red-600" /></Button>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
                        {isSupabaseTour(tour) ? (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleFeatured(tour)}
                              className="h-8 w-8 p-0"
                              title={tour.featured ? "Remove from featured" : "Add to featured"}
                            >
                              <Star className={`w-4 h-4 ${tour.featured ? 'text-yellow-500 fill-current' : ''}`} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`/tour/${tour.slug}`, '_blank')}
                              className="h-8 w-8 p-0"
                              title="Preview tour"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                if (tour.creation_method === 'images') {
                                  // Navigate to tour editor in same tab
                                  navigate(`/admin/tours/${tour.id}/edit`);
                                  toast.success('Opening tour editor...');
                                } else {
                                  setEditingTour(tour);
                                }
                              }}
                              className="h-8 w-8 p-0"
                              title={tour.creation_method === 'images' ? "Open tour editor" : "Edit tour details"}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleDeleteTour(tour.id)} className="text-destructive">
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              // Sync/import to Supabase as draft
                              try {
                                const { error } = await supabase.from('tours').insert({
                                  title: tour.title,
                                  description: tour.description,
                                  status: 'draft',
                                  thumbnail_url: tour.thumbnail_url,
                                  platform_tour_id: tour.id,
                                  tour_platform: tour._source,
                                  creation_method: 'import',
                                });
                                if (error) throw error;
                                toast.success('Tour imported as draft in Supabase');
                                refetch();
                              } catch (e) {
                                toast.error('Failed to import tour');
                              }
                            }}
                          >
                            Import as Draft
                          </Button>
                        )}
                      </div>
                      {isSupabaseTour(tour) && (
                        <Select onValueChange={(value) => handleStatusChange(tour.id, value)}>
                          <SelectTrigger className="w-24 sm:w-32 h-8">
                            <SelectValue placeholder={tour.status} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="published">Published</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Tour Edit Modal */}
        {editingTour && (
          <TourEditModal
            tour={editingTour}
            open={!!editingTour}
            onOpenChange={(open) => !open && setEditingTour(null)}
            onSuccess={() => {
              refetch();
              setEditingTour(null);
            }}
          />
        )}
      </div>
    </AdminLayout>
  );
}

export default AdminTours;
