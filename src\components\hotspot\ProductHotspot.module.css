.hotspotMarker {
  position: absolute;
  transform: translate(-50%, -50%);
}

.hotspotMarkerDot {
  width: 1.5rem;
  height: 1.5rem;
  background: #3b82f6;
  border: 2px solid #fff;
  border-radius: 9999px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  animation: pulse 1.5s infinite;
  cursor: pointer;
  transition: transform 0.15s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotspotMarkerDot:hover {
  transform: scale(1.1);
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(59,130,246,0.5); }
  70% { box-shadow: 0 0 0 10px rgba(59,130,246,0); }
  100% { box-shadow: 0 0 0 0 rgba(59,130,246,0); }
}

.hotspotLabel {
  position: absolute;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.2s;
  background: rgba(30,41,59,0.7);
  color: #fff;
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid rgba(255,255,255,0.12);
  min-width: 120px;
  max-width: 240px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  z-index: 15;
}

.hotspotMarker:hover .hotspotLabel,
.hotspotMarker:focus .hotspotLabel,
.hotspotMarker:active .hotspotLabel {
  opacity: 1;
}

.hotspotEditControls {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  gap: 0.25rem;
  z-index: 20;
}

.hotspotMarker:hover .hotspotEditControls,
.hotspotMarker:focus .hotspotEditControls {
  opacity: 1;
}

.glass-overlay {
  background: rgba(255,255,255,0.05);
  backdrop-filter: blur(0.5px);
  -webkit-backdrop-filter: blur(0.5px);
  border: 1px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.glass-overlay:hover {
  background: rgba(255,255,255,0.02);
}
