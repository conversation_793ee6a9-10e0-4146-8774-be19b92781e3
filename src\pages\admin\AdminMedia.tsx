
import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Upload, Search, Grid, List, Image as ImageIcon, Video, File, Trash2, Download, Eye, FolderOpen } from 'lucide-react';
import AdminLayout from '@/layouts/AdminLayout';
import { toast } from 'sonner';
import { assetManager, type AssetMetadata } from '@/lib/assets/assetManager';
import { supabase } from '@/lib/supabase';

// Helper to get file type from format/mime
const getFileType = (format: string, mimeType: string): 'image' | 'video' | 'document' => {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  return 'document';
};


const AdminMedia = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [assets, setAssets] = useState<AssetMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [usageMap, setUsageMap] = useState<Record<string, string[]>>({});

  // Fetch all assets from DB
  useEffect(() => {
    setIsLoading(true);
    assetManager.listAssets(1, 200)
      .then(({ assets }) => setAssets(assets))
      .finally(() => setIsLoading(false));
  }, []);

  // Fetch usage context for each asset (where used in tours/scenes)
  useEffect(() => {
    const fetchUsage = async () => {
      // Query all tours and scenes that may reference asset URLs
      const { data: tours } = await supabase.from('tours').select('id,title,thumbnail_url,scenes');
      const usage: Record<string, string[]> = {};
      if (tours) {
        for (const asset of assets) {
          const usedIn: string[] = [];
          for (const tour of tours) {
            // Check tour thumbnail
            if (tour.thumbnail_url && tour.thumbnail_url.includes(asset.filename)) {
              usedIn.push(`Tour: ${tour.title}`);
            }
            // Check scenes (if array)
            if (Array.isArray(tour.scenes)) {
              for (const scene of tour.scenes) {
                if (scene && typeof scene === 'object') {
                  if (scene.panorama && scene.panorama.includes(asset.filename)) {
                    usedIn.push(`Scene: ${scene.name || scene.id} (Tour: ${tour.title})`);
                  }
                  if (scene.thumbnail && scene.thumbnail.includes(asset.filename)) {
                    usedIn.push(`Scene Thumbnail: ${scene.name || scene.id} (Tour: ${tour.title})`);
                  }
                }
              }
            }
          }
          usage[asset.id] = usedIn;
        }
      }
      setUsageMap(usage);
    };
    if (assets.length > 0) fetchUsage();
  }, [assets]);


  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };


  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return ImageIcon;
      case 'video': return Video;
      default: return File;
    }
  };


  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    for (const file of Array.from(files)) {
      try {
        await assetManager.uploadAsset(file);
        toast.success(`${file.name} uploaded successfully`);
      } catch (error) {
        const err = error as Error;
        toast.error(`Failed to upload ${file.name}: ${err.message || err}`);
      }
    }
    // Refresh asset list
    setIsLoading(true);
    assetManager.listAssets(1, 200)
      .then(({ assets }) => setAssets(assets))
      .finally(() => setIsLoading(false));
    event.target.value = '';
  };


  const handleDeleteFile = async (assetId: string) => {
    try {
      await assetManager.deleteAsset(assetId);
      toast.success('File deleted successfully');
      setAssets(prev => prev.filter(a => a.id !== assetId));
    } catch (error) {
      const err = error as Error;
      toast.error(`Failed to delete file: ${err.message || err}`);
    }
  };


  const filteredAssets = assets.filter(asset => {
    const matchesSearch = asset.originalName.toLowerCase().includes(searchTerm.toLowerCase());
    const type = getFileType(asset.format, asset.mimeType);
    const matchesType = selectedType === 'all' || type === selectedType;
    return matchesSearch && matchesType;
  });


  const stats = {
    total: assets.length,
    images: assets.filter(a => getFileType(a.format, a.mimeType) === 'image').length,
    videos: assets.filter(a => getFileType(a.format, a.mimeType) === 'video').length,
    documents: assets.filter(a => getFileType(a.format, a.mimeType) === 'document').length,
    totalSize: assets.reduce((acc, a) => acc + a.size, 0)
  };

  return (
    <AdminLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Media Library</h1>
            <p className="text-muted-foreground">Manage images, videos, and documents</p>
          </div>
          <div className="flex gap-2">
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload">
              <Button asChild>
                <span>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Files
                </span>
              </Button>
            </label>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FolderOpen className="w-4 h-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Files</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <ImageIcon className="w-4 h-4 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Images</p>
                  <p className="text-2xl font-bold">{stats.images}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Video className="w-4 h-4 text-purple-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Videos</p>
                  <p className="text-2xl font-bold">{stats.videos}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <File className="w-4 h-4 text-orange-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Documents</p>
                  <p className="text-2xl font-bold">{stats.documents}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div>
                <p className="text-sm text-muted-foreground">Total Size</p>
                <p className="text-2xl font-bold">{formatFileSize(stats.totalSize)}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex gap-2 w-full sm:w-auto">
            <div className="relative flex-1 sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
              title="Filter by file type"
              aria-label="Filter by file type"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Media Grid/List */}

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading media files...</p>
            </div>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {filteredAssets.map((asset) => {
              const type = getFileType(asset.format, asset.mimeType);
              const IconComponent = getFileIcon(type);
              return (
                <Card key={asset.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-square bg-muted flex items-center justify-center relative group">
                    {type === 'image' ? (
                      <img
                        src={asset.optimizedVersions?.preview || asset.optimizedVersions?.original}
                        alt={asset.originalName}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : (
                      <IconComponent className="w-8 h-8 text-muted-foreground" />
                    )}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <Button size="sm" variant="secondary" asChild>
                        <a href={asset.optimizedVersions?.original} target="_blank" rel="noopener noreferrer" title="View original file" aria-label="View original file"><Eye className="w-4 h-4" /></a>
                      </Button>
                      <Button size="sm" variant="secondary" asChild>
                        <a href={asset.optimizedVersions?.original} download title="Download original file" aria-label="Download original file"><Download className="w-4 h-4" /></a>
                      </Button>
                      <Button 
                        size="sm" 
                        variant="destructive"
                        onClick={() => handleDeleteFile(asset.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <CardContent className="p-3">
                    <p className="text-sm font-medium truncate" title={asset.originalName}>
                      {asset.originalName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(asset.size)}
                    </p>
                    {usageMap[asset.id] && usageMap[asset.id].length > 0 && (
                      <div className="mt-2">
                        <span className="text-xs font-semibold">Used in:</span>
                        <ul className="text-xs text-muted-foreground list-disc ml-4">
                          {usageMap[asset.id].map((usage, i) => (
                            <li key={i}>{usage}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="space-y-0">
                {filteredAssets.map((asset, index) => {
                  const type = getFileType(asset.format, asset.mimeType);
                  const IconComponent = getFileIcon(type);
                  return (
                    <div
                      key={asset.id}
                      className={`flex items-center gap-4 p-4 hover:bg-muted/50 ${
                        index !== filteredAssets.length - 1 ? 'border-b' : ''
                      }`}
                    >
                      <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-muted-foreground" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{asset.originalName}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(asset.size)} • {new Date(asset.uploadedAt).toLocaleDateString()}
                        </p>
                        {usageMap[asset.id] && usageMap[asset.id].length > 0 && (
                          <div className="mt-1">
                            <span className="text-xs font-semibold">Used in:</span>
                            <ul className="text-xs text-muted-foreground list-disc ml-4">
                              {usageMap[asset.id].map((usage, i) => (
                                <li key={i}>{usage}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      <Badge variant="secondary">{type}</Badge>
                      <div className="flex gap-2">
                        <Button size="sm" variant="ghost" asChild>
                          <a href={asset.optimizedVersions?.original} target="_blank" rel="noopener noreferrer" title="View original file" aria-label="View original file"><Eye className="w-4 h-4" /></a>
                        </Button>
                        <Button size="sm" variant="ghost" asChild>
                          <a href={asset.optimizedVersions?.original} download title="Download original file" aria-label="Download original file"><Download className="w-4 h-4" /></a>
                        </Button>
                        <Button 
                          size="sm" 
                          variant="ghost"
                          onClick={() => handleDeleteFile(asset.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {filteredAssets.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <FolderOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No files found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || selectedType !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Upload your first file to get started'
              }
            </p>
            {!searchTerm && selectedType === 'all' && (
              <label htmlFor="file-upload">
                <Button asChild>
                  <span>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Files
                  </span>
                </Button>
              </label>
            )}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminMedia;
