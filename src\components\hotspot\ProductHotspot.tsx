import React, { useCallback } from 'react';
import { ShoppingCart, Edit, Trash2 } from 'lucide-react';
import styles from './ProductHotspot.module.css';

export interface ProductHotspotProps {
  x: number; // percent (0-100)
  y: number; // percent (0-100)
  label?: string;
  product?: {
    id: string;
    title: string;
    price: number;
    image_url?: string;
    url?: string;
  };
  onClick?: (e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>) => void;
  onEdit?: () => void;
  onDelete?: () => void;
  style?: React.CSSProperties;
  className?: string;
  overlayStyle?: 'glass' | 'default';
  icon?: React.ReactNode;
  color?: string;
  settings?: Record<string, unknown>;
  tabIndex?: number;
  ariaLabel?: string;
  showControls?: boolean;
}

const ProductHotspot: React.FC<ProductHotspotProps> = ({
  x,
  y,
  label,
  product,
  onClick,
  onEdit,
  onDelete,
  style = {},
  className = '',
  overlayStyle = 'glass',
  icon,
  color = '#3b82f6',
  settings = {},
  tabIndex = 0,
  ariaLabel,
  showControls = false,
}) => {
  // Keyboard accessibility: Enter/Space triggers click
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick?.(e);
    }
  }, [onClick]);

  return (
    <div
      className={[
        styles.hotspotMarker,
        'group',
        className,
        overlayStyle === 'glass' ? styles['glass-overlay'] : '',
      ].join(' ')}
      style={{ left: `${x}%`, top: `${y}%`, ...style }}
      tabIndex={tabIndex}
      role="button"
      aria-label={ariaLabel || label || product?.title || 'Product Hotspot'}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      data-hotspot-x={x}
      data-hotspot-y={y}
    >
      <div style={{ position: 'relative' }}>
        <div
          className={styles.hotspotMarkerDot}
          style={{ background: color }}
        >
          {icon || <ShoppingCart className="w-4 h-4 text-white" aria-hidden="true" />}
        </div>
        {label && (
          <div className={styles.hotspotLabel}>
            <div className="truncate font-semibold">{label}</div>
            {product && (
              <div className="text-yellow-300 font-bold">₦{product.price.toLocaleString()}</div>
            )}
            {product?.title && (
              <div className="text-xs text-white/80 truncate">{product.title}</div>
            )}
            {product?.url && (
              <a
                href={product.url}
                target="_blank"
                rel="noopener noreferrer"
                className="underline text-blue-200 hover:text-blue-400 text-xs mt-1"
                tabIndex={-1}
                aria-label={`View product ${product.title}`}
                onClick={e => e.stopPropagation()}
              >
                View Product
              </a>
            )}
          </div>
        )}
        {showControls && (
          <div className={styles.hotspotEditControls}>
            {onEdit && (
              <button
                className="rounded-full bg-white/80 hover:bg-white text-blue-700 border border-blue-200 p-1 focus:ring-2 focus:ring-blue-400"
                aria-label="Edit Hotspot"
                tabIndex={0}
                onClick={e => { e.stopPropagation(); onEdit(); }}
                type="button"
              >
                <Edit className="w-3 h-3" />
              </button>
            )}
            {onDelete && (
              <button
                className="rounded-full bg-white/80 hover:bg-white text-red-700 border border-red-200 p-1 focus:ring-2 focus:ring-red-400"
                aria-label="Delete Hotspot"
                tabIndex={0}
                onClick={e => { e.stopPropagation(); onDelete(); }}
                type="button"
              >
                <Trash2 className="w-3 h-3" />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductHotspot;
