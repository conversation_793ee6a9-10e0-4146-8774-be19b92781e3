<div
  class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] sm:p-6"
>
  <div class="mb-6 flex items-center justify-between">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">
      Upcoming Schedule
    </h3>

    <div x-data="{openDropDown: false}" class="relative">
      <button
        @click="openDropDown = !openDropDown"
        :class="openDropDown ? 'text-gray-700 dark:text-white' : 'text-gray-400 hover:text-gray-700 dark:hover:text-white'"
      >
        <svg
          class="fill-current"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.2441 6C10.2441 5.0335 11.0276 4.25 11.9941 4.25H12.0041C12.9706 4.25 13.7541 5.0335 13.7541 6C13.7541 6.9665 12.9706 7.75 12.0041 7.75H11.9941C11.0276 7.75 10.2441 6.9665 10.2441 6ZM10.2441 18C10.2441 17.0335 11.0276 16.25 11.9941 16.25H12.0041C12.9706 16.25 13.7541 17.0335 13.7541 18C13.7541 18.9665 12.9706 19.75 12.0041 19.75H11.9941C11.0276 19.75 10.2441 18.9665 10.2441 18ZM11.9941 10.25C11.0276 10.25 10.2441 11.0335 10.2441 12C10.2441 12.9665 11.0276 13.75 11.9941 13.75H12.0041C12.9706 13.75 13.7541 12.9665 13.7541 12C13.7541 11.0335 12.9706 10.25 12.0041 10.25H11.9941Z"
            fill=""
          />
        </svg>
      </button>
      <div
        x-show="openDropDown"
        @click.outside="openDropDown = false"
        class="absolute right-0 top-full z-40 w-40 space-y-1 rounded-2xl border border-gray-200 bg-white p-2 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark"
      >
        <button
          class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
        >
          View More
        </button>
        <button
          class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
        >
          Delete
        </button>
      </div>
    </div>
  </div>

  <div class="custom-scrollbar max-w-full overflow-x-auto">
    <div class="min-w-[500px]">
      <div class="flex flex-col gap-2">
        <div
          x-data="{checked: false}"
          @click="checked = !checked"
          class="flex cursor-pointer items-center gap-9 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-white/[0.03]"
        >
          <div class="flex items-start gap-3">
            <div
              class="flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
              :class="checked ? 'border-brand-500 dark:border-brand-500 bg-brand-500' : 'bg-white dark:bg-white/0 border-gray-300 dark:border-gray-700' "
            >
              <svg
                :class="checked ? 'block' : 'hidden'"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.6668 3.5L5.25016 9.91667L2.3335 7"
                  stroke="white"
                  stroke-width="1.94437"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div>
              <span
                class="mb-0.5 block text-theme-xs text-gray-500 dark:text-gray-400"
              >
                Wed, 11 jan
              </span>
              <span
                class="text-theme-sm font-medium text-gray-700 dark:text-gray-400"
              >
                09:20 AM
              </span>
            </div>
          </div>
          <div>
            <span
              class="mb-1 block text-theme-sm font-medium text-gray-700 dark:text-gray-400"
            >
              Business Analytics Press
            </span>
            <span class="text-theme-xs text-gray-500 dark:text-gray-400">
              Exploring the Future of Data-Driven +6 more
            </span>
          </div>
        </div>

        <div
          x-data="{checked: false}"
          @click="checked = !checked"
          class="flex cursor-pointer items-center gap-9 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-white/[0.03]"
        >
          <div class="flex items-start gap-3">
            <div
              class="flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
              :class="checked ? 'border-brand-500 dark:border-brand-500 bg-brand-500' : 'bg-white dark:bg-white/0 border-gray-300 dark:border-gray-700' "
            >
              <svg
                :class="checked ? 'block' : 'hidden'"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.6668 3.5L5.25016 9.91667L2.3335 7"
                  stroke="white"
                  stroke-width="1.94437"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div>
              <span
                class="mb-0.5 block text-theme-xs text-gray-500 dark:text-gray-400"
              >
                Fri, 15 feb
              </span>
              <span
                class="text-theme-sm font-medium text-gray-700 dark:text-gray-400"
              >
                10:35 AM
              </span>
            </div>
          </div>
          <div>
            <span
              class="mb-1 block text-theme-sm font-medium text-gray-700 dark:text-gray-400"
            >
              Business Sprint
            </span>
            <span class="text-theme-xs text-gray-500 dark:text-gray-400">
              Techniques from Business Sprint +2 more
            </span>
          </div>
        </div>

        <div
          x-data="{checked: false}"
          @click="checked = !checked"
          class="flex cursor-pointer items-center gap-9 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-white/[0.03]"
        >
          <div class="flex items-start gap-3">
            <div
              class="flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
              :class="checked ? 'border-brand-500 dark:border-brand-500 bg-brand-500' : 'bg-white dark:bg-white/0 border-gray-300 dark:border-gray-700' "
            >
              <svg
                :class="checked ? 'block' : 'hidden'"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.6668 3.5L5.25016 9.91667L2.3335 7"
                  stroke="white"
                  stroke-width="1.94437"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div>
              <span
                class="mb-0.5 block text-theme-xs text-gray-500 dark:text-gray-400"
              >
                Thu, 18 mar
              </span>
              <span
                class="text-theme-sm font-medium text-gray-700 dark:text-gray-400"
              >
                1:15 AM
              </span>
            </div>
          </div>
          <div>
            <span
              class="mb-1 block text-theme-sm font-medium text-gray-700 dark:text-gray-400"
            >
              Customer Review Meeting
            </span>
            <span class="text-theme-xs text-gray-500 dark:text-gray-400">
              Insights from the Customer Review Meeting +8 more
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
