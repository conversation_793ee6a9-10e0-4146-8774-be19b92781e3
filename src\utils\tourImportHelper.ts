import { supabase } from '@/lib/supabase';
import { sampleTours } from '@/data/sampleTours';
import { createTourSlug } from '@/lib/slugUtils';

/**
 * Tour Import Helper
 * Safely imports curated tours into the database with proper validation
 * Ensures no source branding and proper display functionality
 */

export interface ImportableTour {
  title: string;
  description: string;
  category: 'property' | 'education' | 'hospitality' | 'tourism' | 'culture' | 'commercial' | 'healthcare' | 'government';
  location: string;
  embed_url: string;
  embed_type: 'iframe' | 'link' | 'custom';
  business_type?: string;
  business_name?: string;
  contact_email?: string;
  website?: string;
}

/**
 * Validates that a tour URL will display properly without source lockouts
 */
export const validateTourUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    
    // Check if it's from a supported platform
    const supportedPlatforms = [
      'panoee.com',
      'tourmkr.com', 
      'massinteract.com'
    ];
    
    const isSupported = supportedPlatforms.some(platform => 
      urlObj.hostname.includes(platform)
    );
    
    if (!isSupported) {
      console.warn(`Unsupported platform: ${urlObj.hostname}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Invalid URL:', error);
    return false;
  }
};

/**
 * Sanitizes tour data to remove any source branding
 */
export const sanitizeTourData = (tour: ImportableTour): ImportableTour => {
  return {
    ...tour,
    title: tour.title
      .replace(/panoee/gi, '')
      .replace(/tourmkr/gi, '')
      .replace(/massinteract/gi, '')
      .replace(/matterport/gi, '')
      .replace(/virtual tour by/gi, '')
      .replace(/tour by/gi, '')
      .trim(),
    description: tour.description
      .replace(/panoee/gi, '')
      .replace(/tourmkr/gi, '')
      .replace(/massinteract/gi, '')
      .replace(/matterport/gi, '')
      .replace(/virtual tour by/gi, '')
      .replace(/tour by/gi, '')
      .replace(/created by/gi, '')
      .trim(),
    business_name: tour.business_name
      ?.replace(/panoee/gi, '')
      .replace(/tourmkr/gi, '')
      .replace(/massinteract/gi, '')
      .trim()
  };
};

/**
 * Imports a single tour into the database
 */
export const importTour = async (
  tourData: ImportableTour, 
  userId: string
): Promise<{ success: boolean; tourId?: string; error?: string }> => {
  try {
    // Validate URL
    if (!validateTourUrl(tourData.embed_url)) {
      return { success: false, error: 'Tour URL is not from a supported platform or may have display issues' };
    }
    
    // Sanitize data
    const cleanTour = sanitizeTourData(tourData);
    
    // Generate unique slug
    const baseSlug = createTourSlug(cleanTour.title);
    const { data: existingTours } = await supabase
      .from('tours')
      .select('slug')
      .like('slug', `${baseSlug}%`);
    
    const existingSlugs = existingTours?.map(t => t.slug).filter(Boolean) || [];
    let uniqueSlug = baseSlug;
    let counter = 1;
    
    while (existingSlugs.includes(uniqueSlug)) {
      uniqueSlug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    // Insert tour
    const { data, error } = await supabase
      .from('tours')
      .insert({
        title: cleanTour.title,
        description: cleanTour.description,
        category: cleanTour.category,
        location: cleanTour.location,
        embed_url: cleanTour.embed_url,
        embed_type: cleanTour.embed_type,
        business_type: cleanTour.business_type,
        business_name: cleanTour.business_name,
        contact_email: cleanTour.contact_email,
        website: cleanTour.website,
        slug: uniqueSlug,
        user_id: userId,
        status: 'published',
        featured: false,
        scenes_count: 1,
        views: 0
      })
      .select()
      .single();
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true, tourId: data.id };
    
  } catch (error) {
    return { success: false, error: `Failed to import tour: ${error}` };
  }
};

/**
 * Imports multiple curated tours from our sample collection
 */
export const importCuratedTours = async (
  userId: string,
  tourIds?: string[]
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const results = { success: 0, failed: 0, errors: [] as string[] };
  
  // Filter tours to import
  const toursToImport = tourIds 
    ? sampleTours.filter(tour => tourIds.includes(tour.id))
    : sampleTours;
  
  for (const sampleTour of toursToImport) {
    const tourData: ImportableTour = {
      title: sampleTour.title,
      description: sampleTour.description,
      category: sampleTour.category,
      location: sampleTour.location,
      embed_url: sampleTour.embed_url,
      embed_type: sampleTour.embed_type,
      business_type: sampleTour.business_type
    };
    
    const result = await importTour(tourData, userId);
    
    if (result.success) {
      results.success++;
    } else {
      results.failed++;
      results.errors.push(`${sampleTour.title}: ${result.error}`);
    }
    
    // Small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
};

/**
 * Gets available tours for import (not already in database)
 */
export const getAvailableToursForImport = async (): Promise<typeof sampleTours> => {
  try {
    // Get existing tour titles to avoid duplicates
    const { data: existingTours } = await supabase
      .from('tours')
      .select('title, embed_url');
    
    const existingTitles = new Set(existingTours?.map(t => t.title.toLowerCase()) || []);
    const existingUrls = new Set(existingTours?.map(t => t.embed_url) || []);
    
    // Filter out tours that already exist
    return sampleTours.filter(tour => 
      !existingTitles.has(tour.title.toLowerCase()) && 
      !existingUrls.has(tour.embed_url)
    );
    
  } catch (error) {
    console.error('Error checking existing tours:', error);
    return sampleTours;
  }
};

/**
 * Preview tour data before import
 */
export const previewTourImport = (tourData: ImportableTour) => {
  const sanitized = sanitizeTourData(tourData);
  const isValid = validateTourUrl(tourData.embed_url);
  
  return {
    original: tourData,
    sanitized,
    isValid,
    warnings: isValid ? [] : ['URL may not display properly - unsupported platform']
  };
};
