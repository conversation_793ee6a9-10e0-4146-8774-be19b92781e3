import { supabase } from '@/lib/supabase';
import { validateTourUrlEnhanced, type UrlValidationResult } from './tourImportHelper';

export interface LinkCheckResult {
  url: string;
  status: 'valid' | 'broken' | 'warning' | 'checking';
  statusCode?: number;
  error?: string;
  warnings: string[];
  lastChecked: Date;
  responseTime?: number;
}

export interface TourLinkReport {
  tourId: string;
  tourTitle: string;
  embedUrl?: LinkCheckResult;
  hotspotLinks: LinkCheckResult[];
  overallStatus: 'healthy' | 'issues' | 'broken';
  lastChecked: Date;
}

export class BrokenLinkChecker {
  private checkQueue: string[] = [];
  private isProcessing = false;
  private results = new Map<string, LinkCheckResult>();

  /**
   * Check a single URL and return detailed results
   */
  async checkUrl(url: string): Promise<LinkCheckResult> {
    const startTime = Date.now();
    const result: LinkCheckResult = {
      url,
      status: 'checking',
      warnings: [],
      lastChecked: new Date()
    };

    try {
      const validation = await validateTourUrlEnhanced(url);
      
      result.status = validation.isValid && validation.isAccessible ? 'valid' : 'broken';
      result.statusCode = validation.status || undefined;
      result.error = validation.error;
      result.warnings = validation.warnings;
      result.responseTime = Date.now() - startTime;

      // Additional checks for warnings
      if (validation.embedSupport === 'limited') {
        result.status = 'warning';
        result.warnings.push('Limited embed support - may have display restrictions');
      }

      if (validation.embedSupport === 'none') {
        result.status = 'warning';
        result.warnings.push('No known embed support - may not display properly');
      }

    } catch (error) {
      result.status = 'broken';
      result.error = error instanceof Error ? error.message : 'Unknown error';
      result.responseTime = Date.now() - startTime;
    }

    this.results.set(url, result);
    return result;
  }

  /**
   * Check all tour URLs in the database
   */
  async checkAllTours(): Promise<TourLinkReport[]> {
    try {
      const { data: tours, error } = await supabase
        .from('tours')
        .select('id, title, embed_url, hotspots')
        .not('embed_url', 'is', null);

      if (error) throw error;

      const reports: TourLinkReport[] = [];

      for (const tour of tours || []) {
        const report = await this.checkTourLinks(tour);
        reports.push(report);
        
        // Small delay to avoid overwhelming servers
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      return reports;
    } catch (error) {
      console.error('Error checking all tours:', error);
      throw error;
    }
  }

  /**
   * Check links for a specific tour
   */
  async checkTourLinks(tour: {
    id: string;
    title: string;
    embed_url?: string;
    hotspots?: any;
  }): Promise<TourLinkReport> {
    const report: TourLinkReport = {
      tourId: tour.id,
      tourTitle: tour.title,
      hotspotLinks: [],
      overallStatus: 'healthy',
      lastChecked: new Date()
    };

    // Check main embed URL
    if (tour.embed_url) {
      report.embedUrl = await this.checkUrl(tour.embed_url);
    }

    // Check hotspot links
    if (tour.hotspots && Array.isArray(tour.hotspots)) {
      for (const hotspot of tour.hotspots) {
        if (hotspot.product?.permalink) {
          const linkResult = await this.checkUrl(hotspot.product.permalink);
          report.hotspotLinks.push(linkResult);
        }
        
        // Check any other URLs in hotspot data
        if (hotspot.link_url) {
          const linkResult = await this.checkUrl(hotspot.link_url);
          report.hotspotLinks.push(linkResult);
        }
      }
    }

    // Determine overall status
    const allResults = [report.embedUrl, ...report.hotspotLinks].filter(Boolean) as LinkCheckResult[];
    
    if (allResults.some(r => r.status === 'broken')) {
      report.overallStatus = 'broken';
    } else if (allResults.some(r => r.status === 'warning')) {
      report.overallStatus = 'issues';
    } else {
      report.overallStatus = 'healthy';
    }

    return report;
  }

  /**
   * Get cached results for a URL
   */
  getCachedResult(url: string): LinkCheckResult | undefined {
    return this.results.get(url);
  }

  /**
   * Clear all cached results
   */
  clearCache(): void {
    this.results.clear();
  }

  /**
   * Get summary statistics
   */
  getSummary(): {
    total: number;
    valid: number;
    broken: number;
    warnings: number;
    checking: number;
  } {
    const results = Array.from(this.results.values());
    return {
      total: results.length,
      valid: results.filter(r => r.status === 'valid').length,
      broken: results.filter(r => r.status === 'broken').length,
      warnings: results.filter(r => r.status === 'warning').length,
      checking: results.filter(r => r.status === 'checking').length
    };
  }

  /**
   * Export results as JSON
   */
  exportResults(): string {
    const summary = this.getSummary();
    const results = Array.from(this.results.entries()).map(([url, result]) => ({
      url,
      ...result,
      lastChecked: result.lastChecked.toISOString()
    }));

    return JSON.stringify({
      summary,
      results,
      exportedAt: new Date().toISOString()
    }, null, 2);
  }

  /**
   * Schedule periodic checks
   */
  startPeriodicChecks(intervalMinutes: number = 60): void {
    setInterval(async () => {
      try {
        await this.checkAllTours();
        console.log('Periodic link check completed');
      } catch (error) {
        console.error('Periodic link check failed:', error);
      }
    }, intervalMinutes * 60 * 1000);
  }
}

// Export singleton instance
export const brokenLinkChecker = new BrokenLinkChecker();

/**
 * Quick utility to check if a URL is likely to work in an iframe
 */
export const checkIframeCompatibility = async (url: string): Promise<{
  compatible: boolean;
  reason?: string;
  suggestions: string[];
}> => {
  const suggestions: string[] = [];
  
  try {
    const urlObj = new URL(url);
    
    // Check for known iframe-blocking domains
    const blockedDomains = [
      'youtube.com',
      'facebook.com',
      'twitter.com',
      'instagram.com'
    ];
    
    if (blockedDomains.some(domain => urlObj.hostname.includes(domain))) {
      return {
        compatible: false,
        reason: 'Domain typically blocks iframe embedding',
        suggestions: [
          'Use the platform\'s official embed code instead',
          'Check if the platform provides an embed-friendly URL'
        ]
      };
    }

    // Check protocol
    if (urlObj.protocol !== 'https:') {
      suggestions.push('Use HTTPS URL for better compatibility');
    }

    // Try to detect X-Frame-Options (limited in browser environment)
    try {
      const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
      // Note: We can't actually read headers in no-cors mode
      // This is just to check basic connectivity
    } catch (error) {
      suggestions.push('URL may not be accessible or may block cross-origin requests');
    }

    return {
      compatible: true,
      suggestions
    };

  } catch (error) {
    return {
      compatible: false,
      reason: 'Invalid URL format',
      suggestions: ['Please provide a valid URL']
    };
  }
};
