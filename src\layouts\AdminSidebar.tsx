import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

// Tour-First Admin Routes
const adminRoutes = [
  { path: '/admin', label: 'Dashboard', icon: <svg width="24" height="24" fill="none"><path d="M5.5 3.25C4.257 3.25 3.25 4.257 3.25 5.5V8.99998C3.25 10.2426 4.257 11.25 5.5 11.25H9C10.2426 11.25 11.25 10.2426 11.25 8.99998V5.5C11.25 4.257 10.2426 3.25 9 3.25H5.5Z" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/tours', label: 'Tours', icon: <svg width="24" height="24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/analytics', label: 'Analytics', icon: <svg width="24" height="24" fill="none"><path d="M3 3v18h18" stroke="currentColor" strokeWidth="1.5"/><path d="M7 12l4-4 4 4 6-6" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/featured', label: 'Featured', icon: <svg width="24" height="24" fill="none"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/vendors', label: 'Vendors', icon: <svg width="24" height="24" fill="none"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" strokeWidth="1.5"/><circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="1.5"/><path d="M22 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" strokeWidth="1.5"/><path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/commerce', label: 'Commerce', icon: <svg width="24" height="24" fill="none"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" stroke="currentColor" strokeWidth="1.5"/><line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" strokeWidth="1.5"/><path d="M16 10a4 4 0 0 1-8 0" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/content', label: 'Content', icon: <svg width="24" height="24" fill="none"><rect x="4" y="4" width="16" height="16" rx="2" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/settings', label: 'Settings', icon: <svg width="24" height="24" fill="none"><circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" strokeWidth="1.5"/></svg> },
];

const AdminSidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  return (
    <aside
      className={`fixed left-0 top-0 z-50 flex h-screen w-[290px] flex-col overflow-y-hidden border-r border-gray-200 bg-white px-5 dark:border-gray-800 dark:bg-black transition-all duration-300 ${collapsed ? 'w-[90px]' : 'w-[290px]'}`}
    >
      {/* Sidebar Header */}
      <div className={`flex items-center gap-2 pt-8 pb-7 ${collapsed ? 'justify-center' : 'justify-between'}`}>
        <Link to="/admin/dashboard">
          <span className={`logo ${collapsed ? 'hidden' : ''}`}>
            <img className="dark:hidden" src="/logo.svg" alt="Logo" />
            <img className="hidden dark:block" src="/logo-dark.svg" alt="Logo" />
          </span>
          <img className={`logo-icon ${collapsed ? 'block' : 'hidden'}`} src="/logo-icon.svg" alt="Logo" />
        </Link>
        <button
          type="button"
          className="ml-auto p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={() => setCollapsed(!collapsed)}
        >
          <span className="sr-only">Toggle Sidebar</span>
          <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
      </div>
      {/* Sidebar Menu */}
      <nav className="flex flex-col overflow-y-auto no-scrollbar">
        <ul className="flex flex-col gap-4 mb-6">
          {adminRoutes.map(route => (
            <li key={route.path}>
              <Link
                to={route.path}
                className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors ${location.pathname.startsWith(route.path) ? 'bg-brand-100 text-brand-600 font-semibold' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}`}
              >
                <span className="w-6 h-6">{route.icon}</span>
                <span className={`menu-item-text ${collapsed ? 'hidden lg:inline' : ''}`}>{route.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default AdminSidebar;
