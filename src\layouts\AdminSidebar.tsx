import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

// Example route config - replace with your actual admin routes
const adminRoutes = [
  { path: '/admin/dashboard', label: 'Dashboard', icon: <svg width="24" height="24" fill="none"><path d="M5.5 3.25C4.257 3.25 3.25 4.257 3.25 5.5V8.99998C3.25 10.2426 4.257 11.25 5.5 11.25H9C10.2426 11.25 11.25 10.2426 11.25 8.99998V5.5C11.25 4.257 10.2426 3.25 9 3.25H5.5Z" stroke="currentColor" strokeWidth="1.5"/><path d="M4.75 5.5C4.75 5.08579 5.08579 4.75 5.5 4.75H9C9.41421 4.75 9.75 5.08579 9.75 5.5V8.99998C9.75 9.41419 9.41421 9.74998 9 9.74998H5.5C5.08579 9.74998 4.75 9.41419 4.75 8.99998V5.5Z" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/tours/list', label: 'Tours', icon: <svg width="24" height="24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/pages', label: 'Pages', icon: <svg width="24" height="24" fill="none"><rect x="4" y="4" width="16" height="16" rx="2" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/vendors', label: 'Vendors', icon: <svg width="24" height="24" fill="none"><path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/overlays', label: 'Overlays', icon: <svg width="24" height="24" fill="none"><rect x="6" y="6" width="12" height="12" rx="2" stroke="currentColor" strokeWidth="1.5"/></svg> },
  { path: '/admin/users', label: 'Users', icon: <svg width="24" height="24" fill="none"><circle cx="12" cy="8" r="4" stroke="currentColor" strokeWidth="1.5"/><path d="M4 20C4 16.6863 7.13401 14 11 14H13C16.866 14 20 16.6863 20 20" stroke="currentColor" strokeWidth="1.5"/></svg> },
];

const AdminSidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  return (
    <aside
      className={`fixed left-0 top-0 z-50 flex h-screen w-[290px] flex-col overflow-y-hidden border-r border-gray-200 bg-white px-5 dark:border-gray-800 dark:bg-black transition-all duration-300 ${collapsed ? 'w-[90px]' : 'w-[290px]'}`}
    >
      {/* Sidebar Header */}
      <div className={`flex items-center gap-2 pt-8 pb-7 ${collapsed ? 'justify-center' : 'justify-between'}`}>
        <Link to="/admin/dashboard">
          <span className={`logo ${collapsed ? 'hidden' : ''}`}>
            <img className="dark:hidden" src="/logo.svg" alt="Logo" />
            <img className="hidden dark:block" src="/logo-dark.svg" alt="Logo" />
          </span>
          <img className={`logo-icon ${collapsed ? 'block' : 'hidden'}`} src="/logo-icon.svg" alt="Logo" />
        </Link>
        <button
          className="ml-auto p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={() => setCollapsed(!collapsed)}
        >
          <span className="sr-only">Toggle Sidebar</span>
          <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
      </div>
      {/* Sidebar Menu */}
      <nav className="flex flex-col overflow-y-auto no-scrollbar">
        <ul className="flex flex-col gap-4 mb-6">
          {adminRoutes.map(route => (
            <li key={route.path}>
              <Link
                to={route.path}
                className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors ${location.pathname.startsWith(route.path) ? 'bg-brand-100 text-brand-600 font-semibold' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}`}
              >
                <span className="w-6 h-6">{route.icon}</span>
                <span className={`menu-item-text ${collapsed ? 'hidden lg:inline' : ''}`}>{route.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default AdminSidebar;
